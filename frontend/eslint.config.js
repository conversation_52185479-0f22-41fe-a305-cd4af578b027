// Simple ESLint configuration for Next.js projects

export default [
  {
    ignores: [
      // Dependencies
      'node_modules/**',
      '.pnp/**',
      '.pnp.js',

      // Build outputs
      '.next/**',
      'out/**',
      'build/**',
      'dist/**',

      // Generated files
      'src/generated/**',
      '**/*.generated.*',
      '**/*.d.ts',

      // Cache
      '.npm/**',
      '.eslintcache',
      '.vercel/**',
      '.turbo/**',

      // Config files
      '*.config.js',
      '*.config.ts',
      'next-env.d.ts',

      // Public assets
      'public/**',

      // Prisma
      'prisma/**',
    ],
    rules: {
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      '@typescript-eslint/no-unused-expressions': 'warn',
    },
  },
];
