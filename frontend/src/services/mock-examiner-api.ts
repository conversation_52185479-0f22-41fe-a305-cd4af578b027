import { ExaminerPanelData } from '@/types/examiner-panel';

// Mock data that would normally come from an API
const mockExaminerData: ExaminerPanelData = {
  gamePlan: [
    { text: '• <PERSON> has submitted a heart attack claim on a $30,000 critical illness policy.' },
    { text: '• All of the necessary documents have been submitted with the claim including signatures.' },
    { text: '• All dates related to the illness are within the coverage period.' },
    { text: '• The attending physician\'s statement and cardiac enzymes test support the diagnosis.' },
    { text: '• The ECG test is not as certain and may need further review.' },
  ],
  actionItems: [
    {
      id: 1,
      title: 'Verify all active and valid policies',
      status: 'green',
      decisionSupport: [
        { label: 'Policy Status', value: 'Active' },
        { label: 'Coverage Type', value: 'Critical Illness' },
        { label: 'Claim Type', value: 'Heart Attack' },
        { label: 'Covered Condition', value: 'Yes' },
        { label: 'Waiting Period', value: 'Completed' },
        { label: 'Policy Effective Date', value: '2023-01-01' },
        { label: 'Coverage Amount', value: '$30,000' },
      ]
    },
    {
      id: 2,
      title: 'Employee Statement: Complete, Signed, Validated Dates',
      status: 'green',
      decisionSupport: [
        { label: 'Document Status', value: 'Complete' },
        { label: 'Signature Verified', value: 'Yes' },
        { label: 'Date of Submission', value: '2024-03-15' },
        { label: 'Date of Diagnosis', value: '2025-02-01' },
        { label: 'Date of First Visit', value: '2025-02-01' },
        { label: 'Date of Submission', value: '2025-02-01' },
        { label: 'Date Admitted', value: 'Digital' },
      ]
    },
    {
      id: 3,
      title: 'Attending Physician Statement: Complete, Signed, Valid Dates, Supports Diagnosis',
      status: 'green',
      decisionSupport: [
        { label: 'Document Status', value: 'Complete' },
        { label: 'Signature Verified', value: 'Yes' },
        { label: 'Diagnosis Support', value: 'Yes (Myocardial Infarction)' },
        { label: 'Date of Submission', value: '2024-03-10' },
        { label: 'Date First Consulted', value: '2025-02-01' },
        { label: 'Date of First Symptoms', value: '2025-02-01' },
        { label: 'Date Admitted', value: '2025-02-01' },
      ]
    },
    {
      id: 4,
      title: 'Cardiac enzymes test supports diagnosis',
      status: 'green',
      decisionSupport: [
        { label: 'Test Type', value: 'Cardiac Enzymes' },
        { label: 'Test Result', value: 'Initial value exceeds 99th percentile; Rise confirms myocardial infarction' },
        { label: 'Date of Test', value: '2025-02-01' },
      ]
    },
    {
      id: 5,
      title: 'ECG results not as clear',
      status: 'yellow',
      decisionSupport: [
        { label: 'Test Type', value: 'ECG' },
        { label: 'Result Status', value: 'Unclear' },
        { label: 'Date of Test', value: '2024-03-08' },
        { label: 'Recommendation', value: 'Further Review Needed' },
      ]
    },
  ],
  summary: [
    { timestamp: '10:24 AM', source: 'SYSTEM', text: 'Verified date is within doctor\'s license' },
    { timestamp: '10:25 PM', source: 'SYSTEM', text: 'Checks if name on driver\'s license matches claim details' },
    { timestamp: '10:29 PM', source: 'AGENT', text: 'Verify driver\'s license to prevent fraud' },
  ]
};

// Mock API function to fetch examiner data
export async function fetchExaminerData(): Promise<ExaminerPanelData> {
  // Simulate API delay
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockExaminerData);
    }, 500);
  });
}
