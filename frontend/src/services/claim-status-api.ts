import { fetchRecords } from "@/lib/airtable";
import {
  AirTableClaimStatusRecord,
  AirtableDocumentStatusRecord,
  AirtableTable,
} from "@/types/airtable";
import { ClaimStatus } from "@/types/examiner-panel";
import { useExaminerPanelStore } from "@/store/useExaminerPanelStore";

/**
 * Fetches claim status from the API or cache
 * @param claimId The ID of the claim to fetch status for
 * @param options Optional configuration for the fetch operation
 * @returns Promise with the claim status data
 */
export async function fetchClaimStatus(
  claimId: number,
  options: {
    useCache?: boolean;
    forceRefresh?: boolean;
  } = { useCache: true, forceRefresh: false }
): Promise<ClaimStatus | null> {
  try {
    const { useCache = true, forceRefresh = false } = options;

    // Check if we should use the cache and if there's cached data available
    if (useCache && !forceRefresh) {
      const cachedStatus = useExaminerPanelStore.getState().getCachedClaimStatus(claimId);

      // If we have cached data, return it
      if (cachedStatus) {
        console.log(`Using cached claim status for ID: ${claimId}`);

        // Add a small artificial delay when using cached data to prevent rapid API calls
        // This helps avoid potential race conditions and UI flickering
        await new Promise(resolve => setTimeout(resolve, 50));

        // Ensure the cached data has the expected structure
        // This is important for the UI to render correctly
        if (cachedStatus.gameplan && cachedStatus.actions) {
          console.log(`Cached data has gameplan (${cachedStatus.gameplan.Status}) and actions (${cachedStatus.actions.Status})`);

          // Log more details about the cached data
          if (cachedStatus.actions.Notes) {
            try {
              const actionsData = JSON.parse(cachedStatus.actions.Notes);
              console.log(`Cached actions data is ${Array.isArray(actionsData) ? 'array' : 'object'} with ${
                Array.isArray(actionsData) ? actionsData.length :
                (actionsData.items ? actionsData.items.length : 0)
              } items`);
            } catch (e) {
              console.error("Error parsing cached actions data:", e);
            }
          }
        } else {
          console.warn(`Cached data is missing gameplan or actions for ID: ${claimId}`);
        }

        return cachedStatus;
      }
    }

    // If we're here, either cache is disabled, we're forcing a refresh, or there's no cached data
    console.log(`Fetching fresh claim status for ID: ${claimId}`);

    // Call our internal API endpoint which handles the authentication
    const response = await fetch(`/api/claim-status/${claimId}`);

    if (!response.ok) {
      console.error(
        `Error fetching claim status: ${response.status} ${response.statusText}`,
      );
      return null;
    }

    const data = await response.json();
    let statusData: ClaimStatus | null = null;

    // The API returns an array with a single item, so we take the first item
    if (Array.isArray(data) && data.length > 0) {
      statusData = data[0];
    } else if (data.Status) {
      statusData = data;
    }

    // If we got valid data, always cache it regardless of useCache setting
    // This ensures we always update the cache with the latest data
    if (statusData) {
      useExaminerPanelStore.getState().cacheClaimStatus(claimId, statusData);
    }

    return statusData;
  } catch (error) {
    console.error("Error fetching claim status:", error);
    return null;
  }
}

export async function fetchClaimStatusAsync(
  claimId: number,
): Promise<ClaimStatus | null> {
  try {
    const statusResponse = fetchRecords<AirTableClaimStatusRecord>(
      AirtableTable.STATUS,
      {
        filterByFormula: `{Claim ID} = '${claimId}'`,
      },
    );
    const documentStatusResponse = fetchRecords<AirtableDocumentStatusRecord>(
      AirtableTable.DOCUMENT_STATUS,
      {
        filterByFormula: `{Claim ID} = '${claimId}'`,
      },
    );

    // Wait for both requests to complete
    await Promise.all([statusResponse, documentStatusResponse]);

    // For now, just return a placeholder response
    // In a real implementation, we would process the responses
    const res: ClaimStatus = {
      "Claim ID": 0,
      Status: "",
      documents: [],
      notes: [],
      gameplan: {
        id: "",
        createdTime: "",
        "Claim ID": 0,
        Notes: "",
        Status: "NaN",
      },
      actions: {
        id: "",
        createdTime: "",
        "Claim ID": 0,
        Notes: "",
        Status: "NaN",
      },
    };
    return res;
  } catch (error) {
    console.error("Error fetching claim status:", error);
    return null;
  }
}
