import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface PageState {
  isClaimDetailPageOpen: boolean;
  isSubmitClaimPageOpen: boolean;
  setClaimDetailPageOpen: (isOpen: boolean) => void;
  setSubmitClaimPageOpen: (isOpen: boolean) => void;
}

// Create a store with persistence to avoid hydration mismatches
export const usePageStore = create<PageState>()(
  persist(
    (set) => ({
      isClaimDetailPageOpen: false,
      isSubmitClaimPageOpen: false,
      setClaimDetailPageOpen: (isOpen) => set({ isClaimDetailPageOpen: isOpen }),
      setSubmitClaimPageOpen: (isOpen) => set({ isSubmitClaimPageOpen: isOpen }),
    }),
    {
      name: 'page-store',
      // Use custom storage that's safe for SSR
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          // Check if window is defined (client-side)
          if (typeof window !== 'undefined') {
            return JSON.parse(window.localStorage.getItem(name) || 'null');
          }
          return null;
        },
        setItem: (name, value) => {
          if (typeof window !== 'undefined') {
            window.localStorage.setItem(name, JSON.stringify(value));
          }
        },
        removeItem: (name) => {
          if (typeof window !== 'undefined') {
            window.localStorage.removeItem(name);
          }
        },
      })),
      // Skip hydration to prevent mismatch
      skipHydration: true,
    }
  )
);

// For backward compatibility
export const useClaimDetailStore = usePageStore;
