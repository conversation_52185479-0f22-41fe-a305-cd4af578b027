import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { ClaimStatus } from '@/types/examiner-panel';

interface ClaimStatusCache {
  [claimId: number]: {
    status: ClaimStatus;
    timestamp: number;
  };
}

interface ExaminerPanelState {
  initialized: boolean;
  setInitialized: (initialized: boolean) => void;
  currentClaimId: number | null;
  setCurrentClaimId: (claimId: number | null) => void;
  claimStatus: ClaimStatus | null;
  setClaimStatus: (status: ClaimStatus | null) => void;
  // Cache-related state and functions
  claimStatusCache: ClaimStatusCache;
  getCachedClaimStatus: (claimId: number) => ClaimStatus | null;
  cacheClaimStatus: (claimId: number, status: ClaimStatus) => void;
  clearCache: () => void;
}

// Create a store with persistence
export const useExaminerPanelStore = create<ExaminerPanelState>()(
  persist(
    (set, get) => ({
      initialized: false,
      currentClaimId: null,
      claimStatus: null,
      claimStatusCache: {},
      setInitialized: (initialized) => set({ initialized }),
      setCurrentClaimId: (claimId) => {
        // Only update if the claim ID has actually changed
        set((state) => {
          if (state.currentClaimId === claimId) {
            console.log(`ExaminerPanelStore: Claim ID unchanged (${claimId}), skipping update`);
            return {}; // No change
          }
          console.log(`ExaminerPanelStore: Setting current claim ID to ${claimId}`);
          return { currentClaimId: claimId };
        });
      },
      setClaimStatus: (status) => {
        // Only update if the status has actually changed
        set((state) => {
          if (state.claimStatus && status && state.claimStatus["Claim ID"] === status["Claim ID"]) {
            console.log(`ExaminerPanelStore: Status for claim ID ${status["Claim ID"]} already set, updating`);
          }

          // If we have a valid status, also update the cache
          if (status && status["Claim ID"]) {
            const { cacheClaimStatus } = get();
            cacheClaimStatus(status["Claim ID"], status);
          }

          return { claimStatus: status };
        });
      },
      // Cache management functions
      getCachedClaimStatus: (claimId) => {
        const cache = get().claimStatusCache;
        const cachedData = cache[claimId];

        if (cachedData) {
          console.log(`ExaminerPanelStore: Found cached status for claim ID ${claimId}, timestamp: ${new Date(cachedData.timestamp).toLocaleTimeString()}`);
          return cachedData.status;
        }

        console.log(`ExaminerPanelStore: No cached status found for claim ID ${claimId}`);
        return null;
      },
      cacheClaimStatus: (claimId, status) => {
        console.log(`ExaminerPanelStore: Caching status for claim ID ${claimId}`);
        set((state) => ({
          claimStatusCache: {
            ...state.claimStatusCache,
            [claimId]: {
              status,
              timestamp: Date.now()
            }
          }
        }));
      },
      clearCache: () => {
        console.log('ExaminerPanelStore: Clearing claim status cache');
        set({ claimStatusCache: {} });
      },
    }),
    {
      name: 'examiner-panel-store',
      // Use custom storage that's safe for SSR
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          // Check if window is defined (client-side)
          if (typeof window !== 'undefined') {
            try {
              const value = window.localStorage.getItem(name);
              return value ? JSON.parse(value) : null;
            } catch (error) {
              console.error('Error parsing stored value:', error);
              return null;
            }
          }
          return null;
        },
        setItem: (name, value) => {
          if (typeof window !== 'undefined') {
            try {
              window.localStorage.setItem(name, JSON.stringify(value));
            } catch (error) {
              console.error('Error storing value:', error);
            }
          }
        },
        removeItem: (name) => {
          if (typeof window !== 'undefined') {
            window.localStorage.removeItem(name);
          }
        },
      })),
      // Skip hydration to prevent mismatch
      skipHydration: true,
    }
  )
);

// Initialize the store on the client side
if (typeof window !== 'undefined') {
  const { setInitialized } = useExaminerPanelStore.getState();

  // Make sure we only run this once
  if (!useExaminerPanelStore.getState().initialized) {
    // Hydrate the store
    useExaminerPanelStore.persist.rehydrate();
    setInitialized(true);
  }
}
