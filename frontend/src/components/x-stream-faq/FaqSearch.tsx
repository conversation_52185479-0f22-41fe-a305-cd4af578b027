"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { Source, ContextSearchDocument } from "@/lib/x-stream-faq";

interface FaqSearchProps {
  className?: string;
}

interface FaqResponse {
  response: string;
  factuality: {
    isFactual: boolean;
    score: string;
  };
  sources: Source[];
  id: string;
  contextSearchDocuments: ContextSearchDocument[];
}

export function FaqSearch({ className }: FaqSearchProps) {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<FaqResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/x-stream-faq/answer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to get answer");
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error("Error searching FAQ:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>X-Stream FAQ Search</CardTitle>
          <CardDescription>
            Ask a question about the claim documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Input
              placeholder="What is the name on the Cardiac Enzyme Test Report?"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSearch();
                }
              }}
              className="flex-1"
            />
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                "Search"
              )}
            </Button>
          </div>

          {error && (
            <div className="mt-4 p-4 bg-red-50 text-red-600 rounded-md">
              {error}
            </div>
          )}

          {result && (
            <div className="mt-4">
              <div className="p-4 bg-green-50 rounded-md">
                <h3 className="font-medium text-green-800">Answer:</h3>
                <p className="mt-1">{result.response}</p>
              </div>

              <div className="mt-4">
                <h3 className="font-medium">Factuality:</h3>
                <p className="mt-1">
                  {result.factuality.isFactual ? "Factual" : "Not factual"} (Score: {result.factuality.score})
                </p>
              </div>

              {result.sources.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium">Sources:</h3>
                  <div className="mt-2 space-y-2">
                    {result.sources.map((source, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-md">
                        <p className="text-sm font-medium">{source.parentDocName}</p>
                        <p className="text-xs text-gray-500 mt-1">{source.title || "No title"}</p>
                        <div className="mt-2 text-sm max-h-32 overflow-y-auto">
                          {source.content}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {result.contextSearchDocuments.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium">Documents:</h3>
                  <ul className="mt-1 list-disc list-inside">
                    {result.contextSearchDocuments.map((doc, index) => (
                      <li key={index}>{doc.name}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="text-xs text-gray-500">
          Powered by Uniphore X-Stream FAQ API
        </CardFooter>
      </Card>
    </div>
  );
}
