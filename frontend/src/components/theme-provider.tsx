"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { usePageStore } from "@/store/usePageStore";

type Theme = "default" | "management" | "submit-claim";

interface ThemeProviderProps {
  children: React.ReactNode;
}

interface ThemeContextType {
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: ThemeProviderProps) {
  const pathname = usePathname();
  const { isClaimDetailPageOpen, isSubmitClaimPageOpen } = usePageStore();
  const [theme, setTheme] = useState<Theme>("default");
  const [mounted, setMounted] = useState(false);

  // Only run theme-setting effects on the client side after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    // Remove all theme classes first
    document.documentElement.classList.remove("management-theme", "submit-claim-theme");

    // Set theme based on route and Zustand store
    if (pathname?.includes("/claims-management") || isClaimDetailPageOpen) {
      setTheme("management");
      document.documentElement.classList.add("management-theme");
    } else if (pathname?.includes("/submit-claim") || isSubmitClaimPageOpen) {
      setTheme("submit-claim");
      document.documentElement.classList.add("submit-claim-theme");
    } else {
      setTheme("default");
    }
  }, [pathname, isClaimDetailPageOpen, isSubmitClaimPageOpen, mounted]);

  return (
    <ThemeContext.Provider value={{ theme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}
