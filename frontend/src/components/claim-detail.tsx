"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { DocumentViewerModal } from "@/components/document-viewer-modal";
import { usePageStore } from "@/store/usePageStore";
import { FileDropzone } from "@/components/ui/file-dropzone";
import { FileValidationOptions } from "@/lib/utils";

interface PolicyDocument {
  id: number;
  policyId: number;
  fileName: string;
  filePath: string;
  uploadedAt: string;
}

interface Policy {
  id: number;
  employeeId: number;
  policyOwner: string;
  insured: string;
  spouse?: string;
  group: string;
  policyNumber: string;
  originalEffectiveDate?: string;
  scheduledEffectiveDate?: string;
  issuedAge?: number;
  insuredCoverage?: number;
  spouseCoverage?: number;
  documents: PolicyDocument[];
}

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  employerName?: string;
  groupId?: string;
  memberId?: string;
  policy?: Policy;
}

interface ClaimDocument {
  id: number;
  claimId: number;
  fileName: string;
  filePath: string;
  uploadedAt: string;
}

interface ClaimComment {
  id: number;
  claimId: number;
  text: string;
  createdAt: string;
}

// Component for displaying a single comment with collapsible content
function CommentItem({ comment }: { comment: ClaimComment }) {
  const [isExpanded, setIsExpanded] = useState(false);
  // Determine if comment is long (more than 3 lines or 150 characters)
  const isLongComment = comment.text.length > 150 || comment.text.split('\n').length > 3;

  // Get preview text (first 150 chars or 3 lines, whichever comes first)
  let previewText = comment.text;
  if (isLongComment && !isExpanded) {
    const lines = comment.text.split('\n').slice(0, 3);
    const joinedLines = lines.join('\n');
    previewText = joinedLines.length > 150 ? joinedLines.substring(0, 150) + '...' : joinedLines + '...';
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <li className={`p-4 transition-all duration-200 ${isExpanded ? 'bg-gray-50' : ''}`}>
      <div className="flex justify-between items-start mb-2">
        <p className="text-sm font-medium flex items-center">
          <span className="mr-2">Comment #{comment.id}</span>
          {isLongComment && (
            <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">
              {isExpanded ? 'Expanded' : 'Collapsed'}
            </span>
          )}
        </p>
        <p className="text-xs text-gray-500">
          {formatDate(comment.createdAt)}
        </p>
      </div>
      <div className={`${isExpanded ? 'bg-white p-3 border rounded-md' : ''}`}>
        <p className="text-sm whitespace-pre-wrap">
          {isExpanded ? comment.text : previewText}
        </p>
        {isLongComment && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs text-blue-600 hover:text-blue-800 mt-2 px-2 py-1 rounded hover:bg-blue-50 focus:outline-none transition-colors"
          >
            {isExpanded ? 'Show less' : 'Show more'}
          </button>
        )}
      </div>
    </li>
  );
}

interface Claim {
  id: number;
  employeeId: number;
  employee: Employee;
  claimType: string;
  description: string;
  incidentDate: string | null;
  dateFiled: string;
  status: string;
  documents: ClaimDocument[];
  comments: ClaimComment[];
}

interface ClaimDetailProps {
  id: string;
}

export function ClaimDetail({ id }: ClaimDetailProps) {
  const [claim, setClaim] = useState<Claim | null>(null);
  const [loading, setLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const statusOptions = ["submitted", "in review", "approved", "rejected"];
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [viewingDocument, setViewingDocument] = useState<{
    id: number;
    fileName: string;
    isPolicyDocument?: boolean;
    policyId?: number;
  } | null>(null);
  const [newComment, setNewComment] = useState("");
  const [isAddingComment, setIsAddingComment] = useState(false);
  const [isClaimInfoExpanded, setIsClaimInfoExpanded] = useState(true);
  const [isEmployeeInfoExpanded, setIsEmployeeInfoExpanded] = useState(true);
  const [isPolicyExpanded, setIsPolicyExpanded] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [fileError, setFileError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [showUploadSection, setShowUploadSection] = useState(false);

  const { setClaimDetailPageOpen, setSubmitClaimPageOpen } = usePageStore();

  const fileValidationOptions: FileValidationOptions = {
    allowedTypes: [
      "application/pdf",
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/gif",
    ],
    maxSizeMB: 25,
    maxFiles: 10,
    minFiles: 1,
    allowDuplicates: false,
  };

  useEffect(() => {
    // Set the detail page as open when this component mounts
    setClaimDetailPageOpen(true);
    setSubmitClaimPageOpen(false);

    // Clean up when component unmounts
    return () => {
      setClaimDetailPageOpen(false);
    };
  }, [setClaimDetailPageOpen, setSubmitClaimPageOpen]);

  // Add keyboard shortcut listener for Ctrl+U
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for Ctrl+U (or Cmd+U on Mac)
      if ((event.ctrlKey || event.metaKey) && event.key === "u") {
        event.preventDefault(); // Prevent default browser behavior
        setShowUploadSection((prevState) => !prevState);

        // If we're showing the upload section, make sure the policy section is expanded
        if (!showUploadSection && !isPolicyExpanded) {
          setIsPolicyExpanded(true);
        }
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyDown);

    // Clean up
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [showUploadSection, isPolicyExpanded]);

  useEffect(() => {
    const fetchClaim = async () => {
      try {
        const response = await fetch(`/api/claims/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch claim");
        }
        const data = await response.json();
        setClaim(data);
        setSelectedStatus(data.status.toLowerCase());
      } catch (error) {
        console.error("Error fetching claim:", error);
        toast.error("Failed to load claim details");
      } finally {
        setLoading(false);
      }
    };

    fetchClaim();
  }, [id]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const handleStatusChange = async () => {
    if (
      !claim ||
      !selectedStatus ||
      selectedStatus === claim.status.toLowerCase()
    )
      return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/claims/${claim.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: selectedStatus }),
      });

      if (!response.ok) {
        throw new Error("Failed to update claim status");
      }

      setClaim({
        ...claim,
        status: selectedStatus,
      });

      toast.success(`Claim #${claim.id} status updated to ${selectedStatus}`);
    } catch (error) {
      console.error("Error updating claim status:", error);
      toast.error("Failed to update claim status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDownloadDocument = async (
    documentId: number,
    fileName: string,
    isPolicyDocument = false,
    policyId?: number,
  ) => {
    if (!claim) return;

    try {
      // Determine the correct API endpoint based on document type
      const endpoint = isPolicyDocument
        ? `/api/policies/${policyId}/documents/${documentId}/download`
        : `/api/claims/${claim.id}/documents/${documentId}/download`;

      const response = await fetch(endpoint);
      if (!response.ok) {
        throw new Error("Failed to download document");
      }

      // Create a blob from the response
      const blob = await response.blob();

      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Downloaded ${fileName}`);
    } catch (error) {
      console.error("Error downloading document:", error);
      toast.error("Failed to download document");
    }
  };

  const getStatusClass = (status: string) => {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case "submitted":
        return "bg-blue-100 text-blue-800 status-submitted";
      case "in review":
        return "bg-yellow-100 text-yellow-800 status-in-review";
      case "approved":
        return "bg-green-100 text-green-800 status-approved";
      case "rejected":
        return "bg-red-100 text-red-800 status-rejected";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleAddComment = async () => {
    if (!claim || !newComment.trim()) return;

    setIsAddingComment(true);
    try {
      const response = await fetch(`/api/claims/${claim.id}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text: newComment }),
      });

      if (!response.ok) {
        throw new Error("Failed to add comment");
      }

      const comment = await response.json();

      // Update the claim with the new comment
      setClaim({
        ...claim,
        comments: [comment, ...claim.comments],
      });

      // Clear the comment input
      setNewComment("");
      toast.success("Comment added successfully");
    } catch (error) {
      console.error("Error adding comment:", error);
      toast.error("Failed to add comment");
    } finally {
      setIsAddingComment(false);
    }
  };

  const handleUploadPolicyDocuments = async () => {
    if (!claim || !claim.employee.policy || uploadFiles.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);
    setFileError(null);

    try {
      // 1. Upload the files
      const formData = new FormData();
      uploadFiles.forEach((file) => formData.append("files", file));

      // Use XMLHttpRequest to track upload progress
      const uploadPromise = new Promise<{ files: any[] }>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open("POST", "/api/upload");

        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            setUploadProgress(Math.round((event.loaded / event.total) * 100));
          }
        };

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              reject(new Error("Invalid response format"));
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        };

        xhr.onerror = () => reject(new Error("Network error during upload"));
        xhr.send(formData);
      });

      const uploadResult = await uploadPromise;
      setUploadProgress(100);

      // 2. Associate the uploaded files with the policy
      const policyId = claim.employee.policy.id;
      const documentPromises = uploadResult.files.map(async (file) => {
        const response = await fetch(`/api/policies/${policyId}/documents`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fileName: file.originalname,
            filePath: file.path,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to associate document: ${file.originalname}`);
        }

        return response.json();
      });

      const newDocuments = await Promise.all(documentPromises);

      // 3. Update the UI with the new documents
      if (claim.employee.policy) {
        const updatedPolicy = {
          ...claim.employee.policy,
          documents: [...newDocuments, ...claim.employee.policy.documents],
        };

        setClaim({
          ...claim,
          employee: {
            ...claim.employee,
            policy: updatedPolicy,
          },
        });
      }

      // 4. Clear the file input
      setUploadFiles([]);
      toast.success("Documents uploaded successfully");
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to upload documents",
      );
      setFileError(
        error instanceof Error ? error.message : "Failed to upload documents",
      );
    } finally {
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">Loading claim details...</div>
    );
  }

  if (!claim) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <h2 className="text-xl font-semibold">Claim not found</h2>
        <p>The claim you are looking for does not exist or has been removed.</p>
        <Link href="/claims-management">
          <Button>Back to Claims Management</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <CardHeader className="px-0 flex-1">
          <CardTitle>Claim #{claim.id} Details</CardTitle>
        </CardHeader>
        <Link href="/claims-management">
          <Button variant="outline">Back to Claims List</Button>
        </Link>
      </div>

      <Card>
        <CardContent className="p-6 space-y-6">
          <div className="space-y-6">
            <div className="border-t pt-4 first:border-t-0 first:pt-0">
              <div
                className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
                onClick={() => setIsClaimInfoExpanded(!isClaimInfoExpanded)}
              >
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-medium">Claim Information</h3>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Click to {isClaimInfoExpanded ? "collapse" : "expand"}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsClaimInfoExpanded(!isClaimInfoExpanded);
                  }}
                >
                  {isClaimInfoExpanded ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-chevron-up"
                    >
                      <path d="m18 15-6-6-6 6" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-chevron-down"
                    >
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                  )}
                </Button>
              </div>

              {isClaimInfoExpanded && (
                <div className="mt-4 space-y-3 animate-in fade-in duration-200">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">Claim Type:</span>
                    <span className="text-sm font-medium col-span-2">
                      {claim.claimType}
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">Date Filed:</span>
                    <span className="text-sm font-medium col-span-2">
                      {formatDate(claim.dateFiled)}
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">
                      Incident Date:
                    </span>
                    <span className="text-sm font-medium col-span-2">
                      {formatDate(claim.incidentDate)}
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">
                      Current Status:
                    </span>
                    <span className="col-span-2">
                      <span
                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(claim.status)}`}
                      >
                        {claim.status}
                      </span>
                    </span>
                  </div>
                  <div className="grid grid-cols-3  col-span-2 ">
                    <span className="text-sm text-gray-500">Description:</span>
                    <div className="border rounded-md bg-gray-50 claim-description-container col-span-2">
                      <p className="text-sm whitespace-pre-wrap">
                        {claim.description}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="border-t pt-4">
              <div
                className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
                onClick={() =>
                  setIsEmployeeInfoExpanded(!isEmployeeInfoExpanded)
                }
              >
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-medium">Employee Information</h3>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Click to {isEmployeeInfoExpanded ? "collapse" : "expand"}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEmployeeInfoExpanded(!isEmployeeInfoExpanded);
                  }}
                >
                  {isEmployeeInfoExpanded ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-chevron-up"
                    >
                      <path d="m18 15-6-6-6 6" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-chevron-down"
                    >
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                  )}
                </Button>
              </div>

              {isEmployeeInfoExpanded && (
                <div className="mt-4 space-y-3 animate-in fade-in duration-200">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">Name:</span>
                    <span className="text-sm font-medium col-span-2">{`${claim.employee.firstName} ${claim.employee.lastName}`}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-sm text-gray-500">Email:</span>
                    <span className="text-sm font-medium col-span-2">
                      {claim.employee.email}
                    </span>
                  </div>
                  {claim.employee.phone && (
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-sm text-gray-500">Phone:</span>
                      <span className="text-sm font-medium col-span-2">
                        {claim.employee.phone}
                      </span>
                    </div>
                  )}
                  {claim.employee.employerName && (
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-sm text-gray-500">Employer:</span>
                      <span className="text-sm font-medium col-span-2">
                        {claim.employee.employerName}
                      </span>
                    </div>
                  )}
                  {claim.employee.groupId && (
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-sm text-gray-500">Group ID:</span>
                      <span className="text-sm font-medium col-span-2">
                        {claim.employee.groupId}
                      </span>
                    </div>
                  )}
                  {claim.employee.memberId && (
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-sm text-gray-500">Member ID:</span>
                      <span className="text-sm font-medium col-span-2">
                        {claim.employee.memberId}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {claim.employee.policy && (
              <div className="border-t pt-4">
                <div
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
                  onClick={() => setIsPolicyExpanded(!isPolicyExpanded)}
                >
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-medium">Policy Information</h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      Click to {isPolicyExpanded ? "collapse" : "expand"}
                    </span>
                    <span className="text-xs text-gray-500 px-2 py-1 rounded border border-gray-200 hidden">
                      Press Ctrl+U to upload documents
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsPolicyExpanded(!isPolicyExpanded);
                    }}
                  >
                    {isPolicyExpanded ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-chevron-up"
                      >
                        <path d="m18 15-6-6-6 6" />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-chevron-down"
                      >
                        <path d="m6 9 6 6 6-6" />
                      </svg>
                    )}
                  </Button>
                </div>

                {isPolicyExpanded && (
                  <div className="mt-4 animate-in fade-in duration-300">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-sm text-gray-500">
                            Policy Owner:
                          </span>
                          <span className="text-sm font-medium col-span-2">
                            {claim.employee.policy.policyOwner}
                          </span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-sm text-gray-500">
                            Insured:
                          </span>
                          <span className="text-sm font-medium col-span-2">
                            {claim.employee.policy.insured}
                          </span>
                        </div>
                        {claim.employee.policy.spouse && (
                          <div className="grid grid-cols-3 gap-2">
                            <span className="text-sm text-gray-500">
                              Spouse:
                            </span>
                            <span className="text-sm font-medium col-span-2">
                              {claim.employee.policy.spouse}
                            </span>
                          </div>
                        )}
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-sm text-gray-500">Group:</span>
                          <span className="text-sm font-medium col-span-2">
                            {claim.employee.policy.group}
                          </span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-sm text-gray-500">
                            Policy Number:
                          </span>
                          <span className="text-sm font-medium col-span-2">
                            {claim.employee.policy.policyNumber}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        {claim.employee.policy.originalEffectiveDate && (
                          <div className="grid grid-cols-3 gap-2">
                            <span className="text-sm text-gray-500">
                              Original Effective Date:
                            </span>
                            <span className="text-sm font-medium col-span-2">
                              {formatDate(
                                claim.employee.policy.originalEffectiveDate,
                              )}
                            </span>
                          </div>
                        )}
                        {claim.employee.policy.scheduledEffectiveDate && (
                          <div className="grid grid-cols-3 gap-2">
                            <span className="text-sm text-gray-500">
                              Scheduled Effective Date:
                            </span>
                            <span className="text-sm font-medium col-span-2">
                              {formatDate(
                                claim.employee.policy.scheduledEffectiveDate,
                              )}
                            </span>
                          </div>
                        )}
                        {claim.employee.policy.issuedAge !== null &&
                          claim.employee.policy.issuedAge !== undefined && (
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-sm text-gray-500">
                                Issued Age:
                              </span>
                              <span className="text-sm font-medium col-span-2">
                                {claim.employee.policy.issuedAge}
                              </span>
                            </div>
                          )}
                        {claim.employee.policy.insuredCoverage !== null &&
                          claim.employee.policy.insuredCoverage !==
                            undefined && (
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-sm text-gray-500">
                                Insured Coverage:
                              </span>
                              <span className="text-sm font-medium col-span-2">
                                $
                                {claim.employee.policy.insuredCoverage.toLocaleString()}
                              </span>
                            </div>
                          )}
                        {claim.employee.policy.spouseCoverage !== null &&
                          claim.employee.policy.spouseCoverage !==
                            undefined && (
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-sm text-gray-500">
                                Spouse Coverage:
                              </span>
                              <span className="text-sm font-medium col-span-2">
                                $
                                {claim.employee.policy.spouseCoverage.toLocaleString()}
                              </span>
                            </div>
                          )}
                      </div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-md font-medium mb-2">
                        Policy Documents{" "}
                        {claim.employee.policy.documents &&
                          claim.employee.policy.documents.length > 0 &&
                          `(${claim.employee.policy.documents.length})`}
                      </h4>

                      {claim.employee.policy.documents &&
                      claim.employee.policy.documents.length > 0 ? (
                        <div className="border rounded-md overflow-hidden mb-4">
                          <ul className="divide-y divide-gray-200">
                            {claim.employee.policy.documents.map((doc) => (
                              <li
                                key={doc.id}
                                className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
                                onClick={() =>
                                  setViewingDocument({
                                    id: doc.id,
                                    fileName: doc.fileName,
                                    isPolicyDocument: true,
                                    policyId: claim.employee.policy?.id,
                                  })
                                }
                              >
                                <div className="flex-1">
                                  <p className="text-sm font-medium">
                                    {doc.fileName}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Uploaded: {formatDate(doc.uploadedAt)}
                                  </p>
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setViewingDocument({
                                        id: doc.id,
                                        fileName: doc.fileName,
                                        isPolicyDocument: true,
                                        policyId: claim.employee.policy?.id,
                                      });
                                    }}
                                  >
                                    View
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDownloadDocument(
                                        doc.id,
                                        doc.fileName,
                                        true,
                                        claim.employee.policy?.id,
                                      );
                                    }}
                                  >
                                    Download
                                  </Button>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 mb-4">
                          No documents attached to this policy
                        </p>
                      )}

                      {showUploadSection && (
                        <div className="mt-6 animate-in fade-in slide-in-from-top-4 duration-300">
                          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                            <p className="text-sm text-blue-700 flex items-center ">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="mr-2"
                              >
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                              </svg>
                              Upload mode activated. Press Ctrl+U again to hide.
                            </p>
                          </div>

                          <h4 className="text-md font-medium mb-2">
                            Upload New Documents
                          </h4>
                          <FileDropzone
                            files={uploadFiles}
                            onChange={setUploadFiles}
                            accept={fileValidationOptions.allowedTypes?.join(
                              ",",
                            )}
                            maxFiles={fileValidationOptions.maxFiles}
                            onError={(errs) =>
                              setFileError(errs.length > 0 ? errs[0] : null)
                            }
                            validationOptions={fileValidationOptions}
                          />

                          {fileError && (
                            <p className="text-sm text-red-500 mt-2">
                              {fileError}
                            </p>
                          )}

                          {uploadProgress !== null && (
                            <div className="mt-2">
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-blue-600 h-2.5 rounded-full"
                                  style={{ width: `${uploadProgress}%` }}
                                ></div>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                Uploading: {uploadProgress}%
                              </p>
                            </div>
                          )}

                          <div className="mt-4 flex justify-end">
                            <Button
                              onClick={handleUploadPolicyDocuments}
                              disabled={isUploading || uploadFiles.length === 0}
                            >
                              {isUploading
                                ? "Uploading..."
                                : "Upload Documents"}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">
                Documents ({claim.documents.length})
              </h3>
              {claim.documents.length > 0 ? (
                <div className="border rounded-md overflow-hidden">
                  <ul className="divide-y divide-gray-200">
                    {claim.documents.map((doc) => (
                      <li
                        key={doc.id}
                        className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
                        onClick={() =>
                          setViewingDocument({
                            id: doc.id,
                            fileName: doc.fileName,
                          })
                        }
                      >
                        <div className="flex-1">
                          <p className="text-sm font-medium">{doc.fileName}</p>
                          <p className="text-xs text-gray-500">
                            Uploaded: {formatDate(doc.uploadedAt)}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              setViewingDocument({
                                id: doc.id,
                                fileName: doc.fileName,
                              });
                            }}
                          >
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadDocument(doc.id, doc.fileName);
                            }}
                          >
                            Download
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No documents attached</p>
              )}
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">
                  Comments ({claim.comments.length})
                </h3>
                {claim.comments.length > 3 && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Showing all comments with collapsible content
                  </span>
                )}
              </div>

              <div className="space-y-4">
                {claim.comments.length > 0 ? (
                  <div className="border rounded-md overflow-hidden">
                    <ul className="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
                      {claim.comments.map((comment) => (
                        <CommentItem key={comment.id} comment={comment} />
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No comments yet</p>
                )}
              </div>

              <div className="flex gap-2 mt-6">
                <Textarea
                  className="flex-1"
                  placeholder="Add a comment..."
                  rows={3}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  disabled={isAddingComment}
                />
                <Button
                  onClick={handleAddComment}
                  disabled={!newComment.trim() || isAddingComment}
                  className="self-end"
                >
                  {isAddingComment ? "Adding..." : "Add Comment"}
                </Button>
              </div>
            </div>

            <div className="border-t pt-6">
              <h3 className="text-lg font-medium mb-4">Update Status</h3>
              <div className="flex items-end gap-4">
                <div className="flex-1">
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      {statusOptions.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  onClick={handleStatusChange}
                  disabled={
                    isUpdating || selectedStatus === claim.status.toLowerCase()
                  }
                >
                  {isUpdating ? "Updating..." : "Update Status"}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewingDocument && claim && (
        <DocumentViewerModal
          documentId={viewingDocument.id}
          claimId={viewingDocument.isPolicyDocument ? undefined : claim.id}
          policyId={
            viewingDocument.isPolicyDocument
              ? viewingDocument.policyId
              : undefined
          }
          fileName={viewingDocument.fileName}
          onClose={() => setViewingDocument(null)}
        />
      )}
    </div>
  );
}
