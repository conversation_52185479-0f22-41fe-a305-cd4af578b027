"use client";

import { usePageStore } from "@/store/usePageStore";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ReactNode, useEffect, useState } from "react";

interface LayoutWrapperProps {
  children: ReactNode;
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const { isClaimDetailPageOpen, isSubmitClaimPageOpen } = usePageStore();
  const [mounted, setMounted] = useState(false);

  // Only run client-side code after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine card classes based on which page is open
  const getCardClasses = () => {
    if (!mounted) return ""; // Default during SSR

    if (isClaimDetailPageOpen) {
      return "mr-17";
    } else if (isSubmitClaimPageOpen) {
      return "submit-claim-theme";
    }
    return "";
  };

  // During SSR or before hydration, render a simpler version
  if (!mounted) {
    return (
      <div className="flex flex-col min-h-screen">
        <Card className="mb-8 relative z-10 ml-1 mt-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Claims Customer Portal</CardTitle>
            <Button variant="outline">Sign Out</Button>
          </CardHeader>
        </Card>
        <main className="flex-1 relative z-11">
          <Card className="p-6 ml-1">
            <CardContent>{children}</CardContent>
          </Card>
        </main>
      </div>
    );
  }

  // Client-side render after hydration
  return (
    <div className="flex flex-col min-h-screen">
      {/* Only show header when not on submit-claim page */}
      {!isSubmitClaimPageOpen && (
        <Card
          className={`mb-8 relative z-10 ${getCardClasses()} ml-1 mt-1`}
        >
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Claims Customer Portal</CardTitle>
            <Button variant="outline">Sign Out</Button>
          </CardHeader>
        </Card>
      )}
      <main className={`flex-1 relative z-11 ${isSubmitClaimPageOpen ? "mt-1" : ""}`}>
        <Card className={`p-6 ${getCardClasses()} ml-1`}>
          <CardContent>{children}</CardContent>
        </Card>
      </main>
    </div>
  );
}
