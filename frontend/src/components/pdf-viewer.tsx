"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";

interface PdfViewerProps {
  url: string;
  fileName: string;
  onDownload: () => void;
}

export function PdfViewer({ url, fileName, onDownload }: PdfViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if the PDF is loading correctly
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, [url]);

  return (
    <div className="w-full h-full flex flex-col">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
          <p>Loading PDF...</p>
        </div>
      )}

      {error ? (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="mb-4 text-red-500">{error}</p>
          <Button onClick={onDownload}>Download PDF Instead</Button>
        </div>
      ) : (
        <div className="w-full h-full overflow-hidden">
          <iframe
            src={url}
            className="w-full h-full border-0"
            style={{ display: 'block' }}
            onLoad={() => setLoading(false)}
            onError={() => {
              setLoading(false);
              setError("Failed to load PDF. Please download the file instead.");
            }}
          />
        </div>
      )}
    </div>
  );
}
