import React, { useState, useEffect, useCallback, useRef } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { CaseUpdateDialog } from "@/components/case-update-dialog";
import { SummaryItem, ClaimStatus } from "@/types/examiner-panel";

// Import custom hooks
import { useExaminerData } from "./hooks/use-examiner-data";
import { useClaimStatus } from "./hooks/use-claim-status";

// Import section components
import { StatusSection } from "./sections/status-section";
import { SummarySection } from "./sections/summary-section";
import { GamePlanSection } from "./sections/game-plan-section";
import { LogSection } from "./sections/log-section";

interface EnhancedExaminerPanelProps {
  claimId: number | null;
}

export function EnhancedExaminerPanel({ claimId }: EnhancedExaminerPanelProps) {
  // Section expansion state
  const [isStatusExpanded, setIsStatusExpanded] = useState(true);
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(true);
  const [isGamePlanExpanded, setIsGamePlanExpanded] = useState(true);
  const [isLogExpanded, setIsLogExpanded] = useState(false);

  // State for each action item's decision support section
  const [expandedDecisionSupports, setExpandedDecisionSupports] = useState<number[]>([]);

  // State for tracking approved and rejected action items
  const [approvedItems, setApprovedItems] = useState<number[]>([]);
  const [rejectedItems, setRejectedItems] = useState<number[]>([]);

  // State for tracking items with visible feedback fields
  const [itemsWithRejectionField, setItemsWithRejectionField] = useState<number[]>([]);
  const [itemsWithApprovalField, setItemsWithApprovalField] = useState<number[]>([]);
  const [rejectionReasons, setRejectionReasons] = useState<Record<number, string>>({});
  const [approvalFeedback, setApprovalFeedback] = useState<Record<number, string>>({});
  // State for tracking rejection types (User or Feedback)
  const [rejectionTypes, setRejectionTypes] = useState<Record<number, "User" | "Feedback">>({});

  // State for case update dialog
  const [showCaseUpdateDialog, setShowCaseUpdateDialog] = useState(false);
  const [caseUpdateSummary, setCaseUpdateSummary] = useState<string>("");
  const [isLoadingCaseUpdate, setIsLoadingCaseUpdate] = useState(false);

  // Use custom hooks
  const {
    data,
    loading,
    error,
    processClaimStatus,
    addSummaryItem,
  } = useExaminerData();

  // Get claim status data
  const { statusSection, claimStatus, isPollingMaxed, isRefreshing, refreshClaimStatus } = useClaimStatus(claimId);

  // Process claim status when it changes
  useEffect(() => {
    if (data && claimStatus) {
      console.log("[EnhancedExaminerPanel] Processing claim status:",
        claimStatus.Status,
        "gameplan:", claimStatus.gameplan?.Status || "none",
        "actions:", claimStatus.actions?.Status || "none"
      );

      // Process the claim status data to update the UI
      // The processClaimStatus function now has its own check to avoid infinite loops
      processClaimStatus(claimStatus);

      // Log the gameplan and actions status for debugging
      if (claimStatus.gameplan) {
        console.log("[EnhancedExaminerPanel] Gameplan status:", claimStatus.gameplan.Status);
        if (claimStatus.gameplan.Notes) {
          console.log("[EnhancedExaminerPanel] Gameplan notes available");
        }
      }

      if (claimStatus.actions) {
        console.log("[EnhancedExaminerPanel] Actions status:", claimStatus.actions.Status);
        if (claimStatus.actions.Notes) {
          console.log("[EnhancedExaminerPanel] Actions notes available");
        }
      }
    }
  }, [data, claimStatus, processClaimStatus]);

  // Update status section expanded state based on claim status
  useEffect(() => {
    if (statusSection) {
      const shouldExpand = !statusSection.claimStatus.includes("Gameplan");
      console.log("[EnhancedExaminerPanel] Status section expanded state:", shouldExpand);
      setIsStatusExpanded(shouldExpand);
    }
  }, [statusSection]);

  // Toggle decision support visibility for an action item
  const toggleDecisionSupport = (index: number) => {
    setExpandedDecisionSupports((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index],
    );
  };

  // Handle the Check button click to approve a claim
  const handleApproveClick = (itemId: number) => {
    if (data && claimId) {
      let actionTitle = "";

      // Try to get the action item title from the API data first
      if (
        claimStatus &&
        claimStatus.actions &&
        claimStatus.actions.Status === "Done"
      ) {
        try {
          const actionsData = JSON.parse(claimStatus.actions.Notes);
          // Handle both array format and object format with items property
          const actionItems = Array.isArray(actionsData)
            ? actionsData
            : (actionsData.items || []);

          if (actionItems.length > itemId) {
            actionTitle = actionItems[itemId].title;
          }
        } catch (error) {
          console.error("Error parsing actions data for approval:", error);
        }
      }

      // If we couldn't get the title from API, try to get it from mock data
      if (!actionTitle && data.actionItems) {
        const actionItem = data.actionItems.find((item) => item.id === itemId);
        if (actionItem) {
          actionTitle = actionItem.title;
        }
      }

      // If we still don't have a title, use a generic one
      if (!actionTitle) {
        actionTitle = `Action item #${itemId}`;
      }

      // Add the item to the approved items list
      setApprovedItems((prev) => [...prev, itemId]);

      // Remove from rejected items if it was previously rejected
      setRejectedItems((prev) => prev.filter((id) => id !== itemId));

      // Make sure decision support is expanded for this item
      if (!expandedDecisionSupports.includes(itemId)) {
        toggleDecisionSupport(itemId);
      }

      // Show the approval feedback field
      setItemsWithApprovalField((prev) =>
        prev.includes(itemId) ? prev : [...prev, itemId]
      );

      // Initialize the approval feedback for this item
      setApprovalFeedback((prev) => ({
        ...prev,
        [itemId]: "",
      }));

      // Create a new summary item
      const newSummaryItem: SummaryItem = {
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
        source: "EXAMINER",
        text: `Action approved: "${actionTitle}"`,
      };

      // Add the new summary item
      addSummaryItem(newSummaryItem);

      // Note: We no longer log to Airtable here
      // Logging will happen only when feedback is provided in handleAutoSaveFeedback
    }
  };

  // Handle the X button click to toggle decision support and show rejection field
  const handleRejectClick = (itemId: number) => {
    // Make sure decision support is expanded for this item
    if (!expandedDecisionSupports.includes(itemId)) {
      toggleDecisionSupport(itemId);
    }

    // Toggle the rejection field visibility
    setItemsWithRejectionField((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId],
    );

    // Initialize or clear the rejection reason for this item
    setRejectionReasons((prev) => ({
      ...prev,
      [itemId]: "",
    }));

    // Initialize the rejection type to "User" by default
    setRejectionTypes((prev) => ({
      ...prev,
      [itemId]: "User",
    }));
  };

  // Handle the change of feedback text (for both approval and rejection)
  const handleFeedbackChange = (itemId: number, text: string) => {
    // Check if this is for a rejection or approval
    if (itemsWithRejectionField.includes(itemId)) {
      setRejectionReasons((prev) => ({
        ...prev,
        [itemId]: text,
      }));
    } else if (itemsWithApprovalField.includes(itemId)) {
      setApprovalFeedback((prev) => ({
        ...prev,
        [itemId]: text,
      }));
    }
  };

  // Handle auto-save for feedback (both approval and rejection)
  const handleAutoSaveFeedback = async (itemId: number) => {
    if (!data || !claimId) return;

    let actionTitle = "";
    let feedbackText = "";
    let isRejection = false;

    // Determine if this is a rejection or approval feedback
    if (itemsWithRejectionField.includes(itemId) && rejectionReasons[itemId]?.trim()) {
      feedbackText = rejectionReasons[itemId];
      isRejection = true;
    } else if (itemsWithApprovalField.includes(itemId) && approvalFeedback[itemId]?.trim()) {
      feedbackText = approvalFeedback[itemId];
      isRejection = false;
    } else {
      // No valid feedback to save
      return;
    }

    // Try to get the action item title from the API data first
    if (
      claimStatus &&
      claimStatus.actions &&
      claimStatus.actions.Status === "Done"
    ) {
      try {
        const actionsData = JSON.parse(claimStatus.actions.Notes);
        // Handle both array format and object format with items property
        const actionItems = Array.isArray(actionsData)
          ? actionsData
          : (actionsData.items || []);

        if (actionItems.length > itemId) {
          actionTitle = actionItems[itemId].title;
        }
      } catch (error) {
        console.error("Error parsing actions data for feedback:", error);
      }
    }

    // If we couldn't get the title from API, try to get it from mock data
    if (!actionTitle && data.actionItems) {
      const actionItem = data.actionItems.find((item) => item.id === itemId);
      if (actionItem) {
        actionTitle = actionItem.title;
      }
    }

    // If we still don't have a title, use a generic one
    if (!actionTitle) {
      actionTitle = `Action item #${itemId}`;
    }

    // For rejections, use the rejection type from state
    const rejectionType = isRejection ? (rejectionTypes[itemId] || "User") : "Process";

    // Create a new summary item
    const newSummaryItem: SummaryItem = {
      timestamp: new Date().toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      }),
      source: "EXAMINER",
      text: isRejection
        ? (rejectionType === "Feedback"
            ? `AI Feedback submitted for "${actionTitle}": ${feedbackText}`
            : `Claim rejected for "${actionTitle}". Reason: ${feedbackText}`)
        : `Feedback provided for approved action "${actionTitle}": ${feedbackText}`,
    };

    // Add the new summary item
    addSummaryItem(newSummaryItem);

    // Send the feedback to the Airtable Claim Notes table
    try {
      const response = await fetch("/api/airtable/claim-notes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          claimId: claimId,
          notes: isRejection
            ? (rejectionType === "Feedback"
                ? `AI Feedback: "${actionTitle}" - ${feedbackText}`
                : `Claim Rejection: "${actionTitle}" - ${feedbackText}`)
            : `Approval Feedback: "${actionTitle}" - ${feedbackText}`,
          createdBy: "user",
          noteType: isRejection ? rejectionType : "Process",
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create claim note: ${response.statusText}`);
      }

      // Show a toast notification that the feedback was saved
      if (isRejection) {
        toast.success(
          rejectionType === "Feedback"
            ? `AI feedback for "${actionTitle}" saved successfully`
            : `Rejection reason for "${actionTitle}" saved successfully`
        );
      } else {
        toast.success(`Approval feedback for "${actionTitle}" saved successfully`);
      }

      console.log(
        `Feedback logged to Airtable for claim ${claimId}: ${actionTitle}. Type: ${isRejection ? rejectionType : "Approval Feedback"}`,
      );
    } catch (error) {
      console.error("Error logging feedback to Airtable:", error);
      toast.error("Failed to save feedback. Please try again.");
    }

    // If this is a rejection, update the rejected items list
    if (isRejection) {
      // Add the item to the rejected items list if not already there
      setRejectedItems((prev) => prev.includes(itemId) ? prev : [...prev, itemId]);

      // Remove from approved items if it was previously approved
      setApprovedItems((prev) => prev.filter((id) => id !== itemId));
    }
  };

  // Legacy function for backward compatibility - now just calls handleAutoSaveFeedback
  const handleRejectConfirm = async (
    itemId: number,
    overrideRejectionType?: "User" | "Feedback",
  ) => {
    // Set the rejection type if provided
    if (overrideRejectionType) {
      setRejectionTypes((prev) => ({
        ...prev,
        [itemId]: overrideRejectionType,
      }));
    }

    // Call the auto-save function
    handleAutoSaveFeedback(itemId);
  };

  // Handle the Update Case button click
  const handleUpdateCase = async () => {
    if (!claimId) {
      toast.error("No claim ID available");
      return;
    }

    setIsLoadingCaseUpdate(true);
    try {
      console.log(`Requesting case update summary for claim ID: ${claimId}`);
      toast.info("Generating case summary...");

      // Call the API to get the case update summary
      const response = await fetch(`/api/update-case/${claimId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch case update: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Received case update summary for claim ID: ${claimId}`);
      setCaseUpdateSummary(data.summary);
      setShowCaseUpdateDialog(true);
    } catch (error) {
      console.error("Error fetching case update:", error);
      toast.error("Failed to generate case summary");
    } finally {
      setIsLoadingCaseUpdate(false);
    }
  };

  // Handle the submission of the edited case update
  const handleSubmitCaseUpdate = async (summary: string) => {
    if (!claimId) {
      toast.error("No claim ID available");
      return;
    }

    try {
      // Call the API to submit the edited case update
      const response = await fetch(`/api/update-case/${claimId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ summary }),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit case update: ${response.statusText}`);
      }

      // Create a new summary item for the log
      if (data) {
        const newSummaryItem: SummaryItem = {
          timestamp: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          source: "EXAMINER",
          text: "Case update submitted",
        };

        // Add the new summary item
        addSummaryItem(newSummaryItem);
      }
    } catch (error) {
      console.error("Error submitting case update:", error);
      throw error; // Re-throw to be handled by the dialog
    }
  };

  return (
    <div className="panel-content h-full flex flex-col overflow-auto relative">
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading examiner data...</span>
        </div>
      ) : error ? (
        <div className="p-4 bg-red-50 text-red-600 rounded-md">
          <p>{error}</p>
          <button
            className="mt-2 px-3 py-1 bg-red-100 rounded-md hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      ) : claimId && !statusSection ? (
        <div className="flex flex-col items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500 mb-2" />
          <span className="text-gray-500">Loading claim status data...</span>
          <span className="text-xs text-gray-400 mt-1">
            Fetching information from API
          </span>
        </div>
      ) : data && statusSection ? (
        <>
          {/* Status Section */}
          <StatusSection
            statusData={statusSection}
            isExpanded={isStatusExpanded}
            onToggle={() => setIsStatusExpanded(!isStatusExpanded)}
            isPollingMaxed={isPollingMaxed}
            isRefreshing={isRefreshing}
            onRefresh={refreshClaimStatus}
          />

          {/* Summary Section */}
          <SummarySection
            claimStatus={claimStatus}
            isExpanded={isSummaryExpanded}
            onToggle={() => setIsSummaryExpanded(!isSummaryExpanded)}
          />

          {/* Game Plan Section */}
          <GamePlanSection
            claimStatus={claimStatus}
            isExpanded={isGamePlanExpanded}
            onToggle={() => setIsGamePlanExpanded(!isGamePlanExpanded)}
            approvedItems={approvedItems}
            rejectedItems={rejectedItems}
            expandedDecisionSupports={expandedDecisionSupports}
            itemsWithRejectionField={itemsWithRejectionField}
            itemsWithApprovalField={itemsWithApprovalField}
            rejectionReasons={rejectionReasons}
            approvalFeedback={approvalFeedback}
            isLoadingCaseUpdate={isLoadingCaseUpdate}
            onToggleDecisionSupport={toggleDecisionSupport}
            onApproveClick={handleApproveClick}
            onRejectClick={handleRejectClick}
            onFeedbackChange={handleFeedbackChange}
            onAutoSaveFeedback={handleAutoSaveFeedback}
            onUpdateCase={handleUpdateCase}
          />

          {/* Log Section */}
          <LogSection
            summaryItems={data.summary}
            isExpanded={isLogExpanded}
            onToggle={() => setIsLogExpanded(!isLogExpanded)}
          />
        </>
      ) : null}

      {/* Case Update Dialog */}
      {showCaseUpdateDialog && (
        <CaseUpdateDialog
          claimId={claimId || 0}
          summary={caseUpdateSummary}
          onClose={() => setShowCaseUpdateDialog(false)}
          onSubmit={handleSubmitCaseUpdate}
        />
      )}
    </div>
  );
}
