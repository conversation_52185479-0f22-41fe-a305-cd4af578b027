/**
 * This file has been refactored into smaller components.
 * See the following files for the implementation details:
 *
 * - enhanced-examiner-panel-refactored.tsx (main component)
 * - hooks/use-examiner-data.ts (data fetching and processing)
 * - hooks/use-claim-status.ts (claim status fetching and processing)
 * - sections/status-section.tsx (Status panel)
 * - sections/summary-section.tsx (Summary panel)
 * - sections/game-plan-section.tsx (Game Plan panel)
 * - sections/log-section.tsx (Log panel)
 * - components/action-item.tsx (Action item component)
 * - components/rejection-form.tsx (Rejection form component)
 * - components/collapsible-section.tsx (Collapsible section component)
 *
 * This file is kept for backward compatibility.
 * New code should import from the index.ts file instead.
 */

// Re-export the refactored component
export { EnhancedExaminerPanel } from './enhanced-examiner-panel-refactored';
