import { useState, useEffect, useCallback } from "react";
import { fetchExaminerData } from "@/services/mock-examiner-api";
import { ExaminerPanelData, SummaryItem, ClaimStatus } from "@/types/examiner-panel";

export function useExaminerData() {
  // State for the data from the API
  const [data, setData] = useState<ExaminerPanelData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data from the mock API
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const examinerData = await fetchExaminerData();
        // Initialize with empty summary array instead of mock summary data
        // Summary will be populated from claim notes
        const modifiedData = {
          ...examinerData,
          summary: [],
        };
        setData(modifiedData);
        setError(null);
      } catch (err) {
        console.error("Error fetching examiner data:", err);
        setError("Failed to load examiner data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Function to process claim status and update the UI
  const processClaimStatus = useCallback((status: ClaimStatus) => {
    if (!data) {
      console.log("[useExaminerData] No data available, skipping status processing");
      return;
    }

    // Check if we've already processed this exact status to avoid infinite loops
    if (data.claimStatus &&
        data.claimStatus["Claim ID"] === status["Claim ID"] &&
        data.claimStatus.Status === status.Status &&
        data.claimStatus.gameplan?.Status === status.gameplan?.Status &&
        data.claimStatus.actions?.Status === status.actions?.Status) {
      console.log("[useExaminerData] Status already processed, skipping to avoid loop");
      return;
    }

    console.log("[useExaminerData] Processing status:",
      status.Status,
      "gameplan:", status.gameplan?.Status || "none",
      "actions:", status.actions?.Status || "none"
    );

    // Log more details about the status for debugging
    if (status.gameplan) {
      console.log("[useExaminerData] Gameplan status:", status.gameplan.Status);
      if (status.gameplan.Notes) {
        console.log("[useExaminerData] Gameplan notes length:", status.gameplan.Notes.length);
      }
    }

    if (status.actions) {
      console.log("[useExaminerData] Actions status:", status.actions.Status);
      if (status.actions.Notes) {
        console.log("[useExaminerData] Actions notes length:", status.actions.Notes.length);
        try {
          const actionsData = JSON.parse(status.actions.Notes);
          console.log("[useExaminerData] Actions data type:",
            Array.isArray(actionsData) ? "Array" : "Object",
            "with", Array.isArray(actionsData) ? actionsData.length :
              (actionsData.items ? actionsData.items.length : 0), "items"
          );
        } catch (e) {
          console.error("[useExaminerData] Error parsing actions data:", e);
        }
      }
    }

    // Safely check if notes property exists
    const notes = status.notes || [];

    // Check if notes array has items
    if (notes.length > 0) {
      // Convert claim notes to summary items
      const notesAsSummaryItems = notes.map((note) => {
        try {
          // Handle potential missing or invalid date
          let timestamp;
          try {
            timestamp = new Date(note.created_at).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            });
          } catch (e) {
            timestamp = "Unknown";
          }

          return {
            timestamp,
            source: note.created_by ? note.created_by.toUpperCase() : "SYSTEM",
            text: note.Notes || "No content",
          };
        } catch (e) {
          return {
            timestamp: "Unknown",
            source: "SYSTEM",
            text: "Error processing note",
          };
        }
      });

      // Identify which summary items were added by the user (EXAMINER source)
      const userAddedItems = data.summary.filter(
        (item) =>
          item.source === "EXAMINER" &&
          (item.text.includes("approved") ||
            item.text.includes("rejected") ||
            !item.text.includes("Status changed")),
      );

      // Update data with claim status and combine API notes with user-added items
      // Put user items at the beginning so they appear at the top
      setData({
        ...data,
        claimStatus: status,
        summary: [...userAddedItems, ...notesAsSummaryItems],
      });
    } else {
      // Still update the claim status even if there are no notes
      setData({
        ...data,
        claimStatus: status,
        // Keep existing summary items if any
        summary: data.summary || [],
      });
    }
  }, [data, setData]);

  // Function to add a new summary item
  const addSummaryItem = useCallback((newItem: SummaryItem) => {
    if (!data) return;

    setData({
      ...data,
      summary: [newItem, ...data.summary],
    });
  }, [data, setData]);

  return {
    data,
    loading,
    error,
    processClaimStatus,
    addSummaryItem,
    setData,
  };
}
