import { useState, useEffect, useRef, useCallback } from "react";
import { fetchClaimStatus } from "@/services/claim-status-api";
import { useExaminerPanelStore } from "@/store/useExaminerPanelStore";
import { ClaimStatus, StatusSectionData } from "@/types/examiner-panel";

// Maximum number of times to poll the API
const MAX_POLL_COUNT = 10;

export function useClaimStatus(claimId: number | null) {
  const { claimStatus: storeClaimStatus, setClaimStatus: setStoreClaimStatus } =
    useExaminerPanelStore();

  // State for status section data
  const [statusSection, setStatusSection] = useState<StatusSectionData | null>(null);

  // State to track if polling has reached the maximum count
  const [isPollingMaxed, setIsPollingMaxed] = useState(false);

  // State to track if a refresh is in progress
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Refs to track polling state
  const pollingRef = useRef<{
    timeoutId: NodeJS.Timeout | null;
    isFetching: boolean;
    lastStatusHash: string;
    fetchCount: number;
  }>({
    timeoutId: null,
    isFetching: false,
    lastStatusHash: "",
    fetchCount: 0
  });

  // Reset state when claim ID changes
  useEffect(() => {
    // Reset claim status when claim ID changes
    setStoreClaimStatus(null);
    setStatusSection(null);

    // Clear any existing timeout
    if (pollingRef.current.timeoutId) {
      clearTimeout(pollingRef.current.timeoutId);
      pollingRef.current.timeoutId = null;
    }

    // Reset polling state
    pollingRef.current.isFetching = false;
    pollingRef.current.lastStatusHash = "";
    pollingRef.current.fetchCount = 0;

    return () => {
      if (pollingRef.current.timeoutId) {
        clearTimeout(pollingRef.current.timeoutId);
        pollingRef.current.timeoutId = null;
      }
    };
  }, [claimId, setStoreClaimStatus]);

  // Update status section data when claim status changes
  useEffect(() => {
    if (storeClaimStatus) {
      // Calculate status section data from claim status
      const gameplanStatus =
        storeClaimStatus.gameplan && storeClaimStatus.gameplan.Status
          ? storeClaimStatus.gameplan.Status
          : "NaN";

      const actionsStatus =
        storeClaimStatus.actions && storeClaimStatus.actions.Status
          ? storeClaimStatus.actions.Status
          : "NaN";

      const documentsCount = storeClaimStatus.documents
        ? storeClaimStatus.documents.length
        : 0;
      const documentsProcessed = storeClaimStatus.documents
        ? storeClaimStatus.documents.filter(
            (doc: any) => doc.Status === "Document Classified",
          ).length
        : 0;

      // Update status section data
      setStatusSection({
        claimStatus: storeClaimStatus.Status,
        documentsCount,
        documentsProcessed,
        gameplanStatus,
        actionsStatus,
      });
    }
  }, [storeClaimStatus]);

  // Helper function to create a hash of the status for comparison
  const createStatusHash = useCallback((status: ClaimStatus): string => {
    return `${status.Status}-${status.gameplan?.Status || 'none'}-${status.actions?.Status || 'none'}-${status.documents?.length || 0}`;
  }, []);

  // Function to fetch data from API or cache
  const fetchData = useCallback(async (isManualRefresh = false) => {
    if (!claimId) return;

    // Skip if already fetching
    if (pollingRef.current.isFetching && !isManualRefresh) {
      console.log(`[useClaimStatus] Already fetching, skipping this cycle`);
      return;
    }

    // Set fetching flag
    pollingRef.current.isFetching = true;
    if (isManualRefresh) {
      setIsRefreshing(true);
    }

    try {
      // Increment fetch count for automatic polling only
      if (!isManualRefresh) {
        pollingRef.current.fetchCount++;
      }
      const fetchCount = pollingRef.current.fetchCount;

      console.log(`[useClaimStatus] ${isManualRefresh ? 'Manual refresh' : `Fetch #${fetchCount}`} for claim ID: ${claimId}`);

      // For manual refresh or first automatic fetch, try to use cached data
      // For subsequent automatic fetches, always get fresh data
      const isFirstFetch = fetchCount === 1;
      const status = await fetchClaimStatus(claimId, {
        useCache: isFirstFetch && !isManualRefresh,
        forceRefresh: !isFirstFetch || isManualRefresh,
      });

      if (status) {
        // Create a hash of the status
        const statusHash = createStatusHash(status);

        // Only update if the status has changed
        if (statusHash !== pollingRef.current.lastStatusHash) {
          console.log(`[useClaimStatus] Status changed (${pollingRef.current.lastStatusHash} -> ${statusHash})`);
          pollingRef.current.lastStatusHash = statusHash;
          setStoreClaimStatus(status);
        } else {
          console.log(`[useClaimStatus] Status unchanged (${statusHash})`);
        }
      } else {
        console.log(`[useClaimStatus] No status received for claim ID: ${claimId}`);
      }
    } catch (error) {
      console.error("[useClaimStatus] Error fetching claim status:", error);
    } finally {
      // Reset fetching flag
      pollingRef.current.isFetching = false;
      if (isManualRefresh) {
        setIsRefreshing(false);
      }

      // Check if we've reached the maximum poll count
      if (!isManualRefresh && pollingRef.current.fetchCount >= MAX_POLL_COUNT) {
        console.log(`[useClaimStatus] Reached maximum poll count (${MAX_POLL_COUNT}), stopping automatic polling`);
        setIsPollingMaxed(true);
        return;
      }

      // Schedule next fetch only if this wasn't a manual refresh
      if (!isManualRefresh) {
        console.log(`[useClaimStatus] Scheduling next fetch in 5 seconds`);
        pollingRef.current.timeoutId = setTimeout(() => fetchData(false), 5000);
      }
    }
  }, [claimId, createStatusHash, setStoreClaimStatus]);

  // Function to manually refresh the claim status
  const refreshClaimStatus = useCallback(() => {
    // Clear any existing timeout
    if (pollingRef.current.timeoutId) {
      clearTimeout(pollingRef.current.timeoutId);
      pollingRef.current.timeoutId = null;
    }

    // Fetch data with manual refresh flag
    fetchData(true);
  }, [fetchData]);

  // Combined fetch and polling for claim status
  useEffect(() => {
    if (!claimId) return;

    // Reset polling maxed state when claim ID changes
    setIsPollingMaxed(false);

    // Skip if we already have data for this claim ID
    if (storeClaimStatus && storeClaimStatus["Claim ID"] === claimId) {
      console.log(`[useClaimStatus] Already have data for claim ID: ${claimId}, skipping initial fetch`);
      // Still set up polling even if we have data
    } else {
      console.log(`[useClaimStatus] Initial fetch for claim ID: ${claimId}`);
    }

    // Start fetching and polling
    console.log(`[useClaimStatus] Starting fetch and polling for claim ID: ${claimId}`);
    fetchData(false);

    // Clean up on unmount
    return () => {
      console.log(`[useClaimStatus] Cleaning up polling for claim ID: ${claimId}`);
      if (pollingRef.current.timeoutId) {
        clearTimeout(pollingRef.current.timeoutId);
        pollingRef.current.timeoutId = null;
      }
    };
  }, [claimId, storeClaimStatus, fetchData]);

  return {
    statusSection,
    claimStatus: storeClaimStatus,
    isPollingMaxed,
    isRefreshing,
    refreshClaimStatus,
  };
}
