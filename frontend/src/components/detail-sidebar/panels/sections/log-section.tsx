import React from "react";
import { CollapsibleSection } from "../components/collapsible-section";
import { SummaryItem } from "@/types/examiner-panel";

interface LogSectionProps {
  summaryItems: SummaryItem[];
  isExpanded: boolean;
  onToggle: () => void;
}

export function LogSection({
  summaryItems,
  isExpanded,
  onToggle,
}: LogSectionProps) {
  return (
    <CollapsibleSection title="Log" isExpanded={isExpanded} onToggle={onToggle}>
      {summaryItems && summaryItems.length > 0 ? (
        summaryItems.map((item, index) => {
          // Determine styling based on source and content
          let className = "text-xs p-2 rounded mb-1";

          if (item.source === "EXAMINER") {
            if (item.text.includes("rejected")) {
              className += " text-red-600 font-medium bg-red-50";
            } else if (item.text.includes("approved")) {
              className += " text-green-600 font-medium bg-green-50";
            } else {
              className += " text-blue-600 font-medium bg-blue-50";
            }
          } else if (item.source === "SYSTEM") {
            className += " text-gray-600 bg-gray-50";
          } else if (item.source === "AGENT") {
            className += " text-purple-600 bg-purple-50";
          } else {
            // For notes from API or other sources
            className += " text-gray-700 bg-gray-100";
          }

          return (
            <div key={index} className={className}>
              <span className="font-semibold">{item.timestamp}</span>{" "}
              - <span className="font-semibold">{item.source}</span>:{" "}
              {item.text}
            </div>
          );
        })
      ) : (
        <div className="text-xs p-2 rounded mb-1 bg-gray-100 text-gray-600">
          No notes available for this claim. Notes will appear here
          when they are added.
        </div>
      )}
    </CollapsibleSection>
  );
}
