import React, { useState } from "react";
import { Loader2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CollapsibleSection } from "../components/collapsible-section";
import { ActionItem } from "../components/action-item";
import { FeedbackForm } from "../components/feedback-form";
import { ManualAnalysisSection } from "../components/manual-analysis-section";
import { ClaimStatus } from "@/types/examiner-panel";
import { toast } from "sonner";

interface GamePlanSectionProps {
  claimStatus: ClaimStatus | null;
  isExpanded: boolean;
  onToggle: () => void;
  approvedItems: number[];
  rejectedItems: number[];
  expandedDecisionSupports: number[];
  itemsWithRejectionField: number[];
  itemsWithApprovalField: number[];
  rejectionReasons: Record<number, string>;
  approvalFeedback: Record<number, string>;
  isLoadingCaseUpdate: boolean;
  onToggleDecisionSupport: (index: number) => void;
  onApproveClick: (index: number) => void;
  onRejectClick: (index: number) => void;
  onFeedbackChange: (index: number, reason: string) => void;
  onAutoSaveFeedback: (index: number) => void;
  onUpdateCase: () => void;
}

export function GamePlanSection({
  claimStatus,
  isExpanded,
  onToggle,
  approvedItems,
  rejectedItems,
  expandedDecisionSupports,
  itemsWithRejectionField,
  itemsWithApprovalField,
  rejectionReasons,
  approvalFeedback,
  isLoadingCaseUpdate,
  onToggleDecisionSupport,
  onApproveClick,
  onRejectClick,
  onFeedbackChange,
  onAutoSaveFeedback,
  onUpdateCase,
}: GamePlanSectionProps) {
  // State for manual analysis section
  const [showManualAnalysis, setShowManualAnalysis] = useState(false);
  const [manualAnalysisText, setManualAnalysisText] = useState("");

  // Handle manual analysis feedback change
  const handleManualAnalysisFeedbackChange = (text: string) => {
    setManualAnalysisText(text);
  };

  // Handle auto-save for manual analysis feedback
  const handleManualAnalysisAutoSave = async () => {
    if (!claimStatus || !manualAnalysisText.trim()) return;

    const claimId = claimStatus["Claim ID"];

    try {
      // Send the manual analysis to the Airtable Claim Notes table
      const response = await fetch("/api/airtable/claim-notes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          claimId: claimId,
          notes: `Manual Analysis: ${manualAnalysisText}`,
          createdBy: "user",
          noteType: "User",
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create claim note: ${response.statusText}`);
      }

      // Show a toast notification that the feedback was saved
      toast.success("Manual analysis saved successfully");

      console.log(`Manual analysis logged to Airtable for claim ${claimId}`);
    } catch (error) {
      console.error("Error logging manual analysis to Airtable:", error);
      toast.error("Failed to save manual analysis. Please try again.");
    }
  };
  return (
    <CollapsibleSection title="Game Plan" isExpanded={isExpanded} onToggle={onToggle}>
      {claimStatus && claimStatus.actions ? (
        (() => {
          // Check if actions is an array or a single object
          const actionsItem = Array.isArray(claimStatus.actions)
            ? claimStatus.actions.length > 0
              ? claimStatus.actions[0]
              : null
            : claimStatus.actions;

          if (!actionsItem) {
            return <p className="text-sm">No actions data available.</p>;
          }

          if (actionsItem.Status === "Done") {
            try {
              if (!actionsItem.Notes) {
                return <p className="text-sm">No actions content available.</p>;
              }

              try {
                // Parse the actions data, handling both array and object formats
                const actionsData = JSON.parse(actionsItem.Notes);

                // Log the parsed data for debugging
                console.log("[GamePlanSection] Parsed actions data:",
                  Array.isArray(actionsData) ? "Array format" : "Object format",
                  actionsData
                );

                // Handle both array format and object format with items property
                const actionItems = Array.isArray(actionsData)
                  ? actionsData
                  : (actionsData.items || []);

                if (actionItems && actionItems.length > 0) {
                  return (
                    <>
                      {actionItems.map((item: any, index: number) => (
                        <div key={index}>
                          <ActionItem
                            id={index}
                            title={item.title}
                            status={item.status}
                            decisionSupport={item.decisionSupport}
                            isApproved={approvedItems.includes(index)}
                            isRejected={rejectedItems.includes(index)}
                            isDecisionSupportExpanded={expandedDecisionSupports.includes(index)}
                            onToggleDecisionSupport={onToggleDecisionSupport}
                            onApprove={onApproveClick}
                            onReject={onRejectClick}
                          />

                          {itemsWithRejectionField.includes(index) && (
                            <FeedbackForm
                              itemId={index}
                              feedbackText={rejectionReasons[index] || ""}
                              isRejection={true}
                              onFeedbackChange={(id, text) => onFeedbackChange(id, text)}
                              onAutoSave={(id) => onAutoSaveFeedback(id)}
                            />
                          )}

                          {itemsWithApprovalField.includes(index) && (
                            <FeedbackForm
                              itemId={index}
                              feedbackText={approvalFeedback[index] || ""}
                              isRejection={false}
                              onFeedbackChange={(id, text) => onFeedbackChange(id, text)}
                              onAutoSave={(id) => onAutoSaveFeedback(id)}
                            />
                          )}
                        </div>
                      ))}

                      {/* Manual Analysis Section */}
                      {showManualAnalysis && (
                        <ManualAnalysisSection
                          feedbackText={manualAnalysisText}
                          onFeedbackChange={handleManualAnalysisFeedbackChange}
                          onAutoSave={handleManualAnalysisAutoSave}
                        />
                      )}

                      {/* Update Case button and Add Manual Analysis button */}
                      <div className="mt-6 flex justify-between items-center">
                        <div className="flex-1"></div> {/* Spacer */}
                        <Button
                          onClick={onUpdateCase}
                          disabled={isLoadingCaseUpdate}
                          className="bg-orange-400 hover:bg-orange-500 text-white"
                        >
                          {isLoadingCaseUpdate ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Generating summary...
                            </>
                          ) : (
                            "Update Case"
                          )}
                        </Button>
                        <div className="flex-1 flex justify-end">
                          {!showManualAnalysis && (
                            <Button
                              onClick={() => setShowManualAnalysis(true)}
                              className="rounded-full bg-orange-400 hover:bg-orange-500 text-white shadow-lg ml-4"
                              size="icon"
                              title="Add Manual Analysis"
                            >
                              <Plus size={20} />
                            </Button>
                          )}
                        </div>
                      </div>
                    </>
                  );
                } else {
                  return <p className="text-sm">No action items available in the data.</p>;
                }
              } catch (jsonError) {
                return (
                  <div className="p-2 bg-yellow-50 text-yellow-600 rounded">
                    <p>Actions data is not in the expected format.</p>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                      {actionsItem.Notes || "Empty"}
                    </pre>
                  </div>
                );
              }
            } catch (error) {
              console.error("Error processing actions data:", error);
              return (
                <div className="p-2 bg-red-50 text-red-600 rounded">
                  <p>Error processing actions data. Raw content:</p>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                    {actionsItem.Notes || "Empty"}
                  </pre>
                </div>
              );
            }
          } else {
            return (
              <div className="flex items-center">
                <Loader2 className="h-4 w-4 animate-spin text-yellow-500 mr-2" />
                <p className="text-sm">Actions are still being generated.</p>
              </div>
            );
          }
        })()
      ) : (
        <div className="flex items-center">
          <p className="text-gray-600 text-sm">
            Actions not yet created. Come back a bit later.
          </p>
        </div>
      )}
    </CollapsibleSection>
  );
}
