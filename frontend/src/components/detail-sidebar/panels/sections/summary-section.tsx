import React from "react";
import { Loader2 } from "lucide-react";
import { CollapsibleSection } from "../components/collapsible-section";
import { ClaimStatus } from "@/types/examiner-panel";

interface SummarySectionProps {
  claimStatus: ClaimStatus | null;
  isExpanded: boolean;
  onToggle: () => void;
}

export function SummarySection({
  claimStatus,
  isExpanded,
  onToggle,
}: SummarySectionProps) {
  return (
    <CollapsibleSection title="Summary" isExpanded={isExpanded} onToggle={onToggle}>
      {claimStatus && claimStatus.gameplan ? (
        (() => {
          if (claimStatus.gameplan.Status === "Done") {
            try {
              if (!claimStatus.gameplan.Notes) {
                return <p>No gameplan content available.</p>;
              }

              try {
                const gamePlanData = JSON.parse(claimStatus.gameplan.Notes);

                if (gamePlanData.steps && gamePlanData.steps.length > 0) {
                  return (
                    <>
                      {gamePlanData.steps.map((step: string, index: number) => (
                        <p key={index}>{step}</p>
                      ))}
                    </>
                  );
                } else {
                  return <p>No gameplan steps available in the data.</p>;
                }
              } catch (jsonError) {
                const textContent = claimStatus.gameplan.Notes;

                if (textContent.includes("\n") || textContent.includes("•")) {
                  const lines = textContent
                    .split(/[\n•]/)
                    .map((line: string) => line.trim())
                    .filter((line: string) => line.length > 0);

                  return (
                    <>
                      {lines.map((line: string, index: number) => (
                        <p key={index}>
                          {line.startsWith("•") ? line : `• ${line}`}
                        </p>
                      ))}
                    </>
                  );
                } else {
                  return <p>{textContent}</p>;
                }
              }
            } catch (error) {
              console.error("Error processing gameplan data:", error);
              return (
                <div className="p-2 bg-red-50 text-red-600 rounded">
                  <p>Error processing gameplan data. Raw content:</p>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                    {claimStatus.gameplan.Notes || "Empty"}
                  </pre>
                </div>
              );
            }
          } else {
            return (
              <div className="flex items-center">
                <Loader2 className="h-4 w-4 animate-spin text-yellow-500 mr-2" />
                <p>Game plan is still being generated.</p>
              </div>
            );
          }
        })()
      ) : (
        <div className="flex items-center">
          <p className="text-gray-600">
            Gameplan not yet created. Come back a bit later.
          </p>
        </div>
      )}
    </CollapsibleSection>
  );
}
