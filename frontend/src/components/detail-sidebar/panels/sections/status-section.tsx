import React from "react";
import { RefreshCw } from "lucide-react";
import { CollapsibleSection } from "../components/collapsible-section";
import { StatusSectionData } from "@/types/examiner-panel";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface StatusSectionProps {
  statusData: StatusSectionData;
  isExpanded: boolean;
  onToggle: () => void;
  isPollingMaxed?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
}

export function StatusSection({
  statusData,
  isExpanded,
  onToggle,
  isPollingMaxed = false,
  isRefreshing = false,
  onRefresh,
}: StatusSectionProps) {
  return (
    <CollapsibleSection
      title="Status"
      isExpanded={isExpanded}
      onToggle={onToggle}
      action={
        onRefresh && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRefresh();
                  }}
                  disabled={isRefreshing}
                >
                  <RefreshCw
                    className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                  />
                  <span className="sr-only">Refresh status</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh claim status</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      }
    >
      <div className="grid grid-cols-2 gap-2">
        {isPollingMaxed && (
          <div className="col-span-2 mb-2 text-xs text-gray-500 bg-gray-100 p-2 rounded">
            Auto-refresh has stopped. Click the refresh button to update.
          </div>
        )}
        <div className="text-gray-600">Claim Status:</div>
        <div className="font-medium">{statusData.claimStatus}</div>

        <div className="text-gray-600">Documents:</div>
        <div className="font-medium">
          {statusData.documentsProcessed} processed of{" "}
          {statusData.documentsCount} uploaded
        </div>

        <div className="text-gray-600">Gameplan Status:</div>
        <div className="font-medium">
          {statusData.gameplanStatus === "Done" ? (
            <span className="text-green-600">Complete</span>
          ) : statusData.gameplanStatus === "Generating" ? (
            <span className="text-yellow-600">In Progress</span>
          ) : (
            <span className="text-gray-600">Not Started</span>
          )}
        </div>

        <div className="text-gray-600">Actions Status:</div>
        <div className="font-medium">
          {statusData.actionsStatus === "Done" ? (
            <span className="text-green-600">Complete</span>
          ) : statusData.actionsStatus === "Generating" ? (
            <span className="text-yellow-600">In Progress</span>
          ) : (
            <span className="text-gray-600">Not Started</span>
          )}
        </div>
      </div>
    </CollapsibleSection>
  );
}
