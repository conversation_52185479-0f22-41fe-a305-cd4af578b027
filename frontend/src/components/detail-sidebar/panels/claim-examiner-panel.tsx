"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { useExaminerPanelStore } from "@/store/useExaminerPanelStore";
import { EnhancedExaminerPanel } from "./enhanced-examiner-panel";

export function ClaimExaminerPanel() {
  const { currentClaimId, setCurrentClaimId } = useExaminerPanelStore();
  const pathname = usePathname();

  // Determine context
  const isLoansContext = pathname?.includes("/loan-management/");

  // Extract ID from URL - works for both claims and loans
  useEffect(() => {
    // Get the ID from the current URL
    const getIdFromUrl = (path: string) => {
      const claimMatch = path.match(/\/claims-management\/(\d+)/);
      const loanMatch = path.match(/\/loan-management\/(\d+)/);

      const match = claimMatch || loanMatch;

      if (match && match[1]) {
        const urlId = parseInt(match[1], 10);
        if (!isNaN(urlId)) {
          return urlId;
        }
      }
      return null;
    };

    // Get the current ID from the URL
    const urlId = getIdFromUrl(pathname || '');

    // Only update if the ID has actually changed
    if (urlId !== currentClaimId) {
      console.log(`ExaminerPanel: URL ID changed from ${currentClaimId} to ${urlId} (context: ${isLoansContext ? 'loans' : 'claims'})`);
      setCurrentClaimId(urlId);
    }
  }, [pathname, currentClaimId, setCurrentClaimId, isLoansContext]);

  return (
    <EnhancedExaminerPanel claimId={currentClaimId} />
  );
}
