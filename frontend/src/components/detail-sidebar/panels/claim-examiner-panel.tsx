"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { useExaminerPanelStore } from "@/store/useExaminerPanelStore";
import { EnhancedExaminerPanel } from "./enhanced-examiner-panel";

export function ClaimExaminerPanel() {
  const { currentClaimId, setCurrentClaimId } = useExaminerPanelStore();
  const pathname = usePathname();

  // Extract claim ID from URL - only when pathname changes
  useEffect(() => {
    // Get the claim ID from the current URL
    const getClaimIdFromUrl = (path: string) => {
      const match = path.match(/\/claims-management\/(\d+)/);

      if (match && match[1]) {
        const urlClaimId = parseInt(match[1], 10);
        if (!isNaN(urlClaimId)) {
          return urlClaimId;
        }
      }
      return null;
    };

    // Get the current claim ID from the URL
    const urlClaimId = getClaimIdFromUrl(pathname || '');

    // Only update if the claim ID has actually changed
    if (urlClaimId !== currentClaimId) {
      console.log(`ClaimExaminerPanel: URL claim ID changed from ${currentClaimId} to ${urlClaimId}`);
      setCurrentClaimId(urlClaimId);
    }
  }, [pathname, currentClaimId, setCurrentClaimId]);

  return (
    <EnhancedExaminerPanel claimId={currentClaimId} />
  );
}
