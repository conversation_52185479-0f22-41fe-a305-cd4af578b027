import React, { useEffect, useRef } from "react";
import { toast } from "sonner";

interface FeedbackFormProps {
  itemId: number;
  feedbackText: string;
  isRejection: boolean;
  onFeedbackChange: (id: number, text: string) => void;
  onAutoSave: (id: number) => void;
}

export function FeedbackForm({
  itemId,
  feedbackText,
  isRejection,
  onFeedbackChange,
  onAutoSave,
}: FeedbackFormProps) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Track the last saved text to prevent duplicate saves
  const lastSavedTextRef = useRef<string>("");

  // Auto-save when user stops typing for 3 seconds
  useEffect(() => {
    // Skip if the text is empty or hasn't changed since last save
    if (!feedbackText.trim() || feedbackText.trim() === lastSavedTextRef.current) {
      return;
    }

    const handleAutoSave = () => {
      if (feedbackText.trim() && feedbackText.trim() !== lastSavedTextRef.current) {
        // Update the last saved text before saving
        lastSavedTextRef.current = feedbackText.trim();
        onAutoSave(itemId);
        // Toast notification is now handled in the parent component
      }
    };

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(handleAutoSave, 3000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [feedbackText, itemId, onAutoSave]);

  const handleBlur = () => {
    // Only save if the text has changed since the last save
    if (feedbackText.trim() && feedbackText.trim() !== lastSavedTextRef.current) {
      // Update the last saved text before saving
      lastSavedTextRef.current = feedbackText.trim();
      onAutoSave(itemId);
      // Toast notification is now handled in the parent component
    }
  };

  return (
    <div className="mt-3 pt-3 border-t border-gray-200">
      <h4 className="text-sm font-medium mb-2 text-gray-700">
        {isRejection
          ? "Feedback on AI Recommendation (required)"
          : "Feedback on AI Recommendation (optional)"}
      </h4>

      <textarea
        ref={textareaRef}
        className="w-full h-24 p-2 border border-gray-300 rounded text-sm mb-2"
        placeholder={isRejection
          ? "Enter your feedback or rejection reason..."
          : "Enter your feedback (optional)..."}
        value={feedbackText}
        onChange={(e) => onFeedbackChange(itemId, e.target.value)}
        onBlur={handleBlur}
      />
    </div>
  );
}
