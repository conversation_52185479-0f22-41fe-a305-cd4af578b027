import React, { useEffect, useRef } from "react";

interface ManualAnalysisSectionProps {
  feedbackText: string;
  onFeedbackChange: (text: string) => void;
  onAutoSave: () => void;
}

export function ManualAnalysisSection({
  feedbackText,
  onFeedbackChange,
  onAutoSave,
}: ManualAnalysisSectionProps) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Track the last saved text to prevent duplicate saves
  const lastSavedTextRef = useRef<string>("");

  // Auto-save when user stops typing for 3 seconds
  useEffect(() => {
    // Skip if the text is empty or hasn't changed since last save
    if (!feedbackText.trim() || feedbackText.trim() === lastSavedTextRef.current) {
      return;
    }

    const handleAutoSave = () => {
      if (feedbackText.trim() && feedbackText.trim() !== lastSavedTextRef.current) {
        // Update the last saved text before saving
        lastSavedTextRef.current = feedbackText.trim();
        onAutoSave();
      }
    };

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(handleAutoSave, 3000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [feedbackText, onAutoSave]);

  const handleBlur = () => {
    // Only save if the text has changed since the last save
    if (feedbackText.trim() && feedbackText.trim() !== lastSavedTextRef.current) {
      // Update the last saved text before saving
      lastSavedTextRef.current = feedbackText.trim();
      onAutoSave();
    }
  };

  return (
    <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded">
      <h4 className="text-lg font-medium mb-4 text-gray-800">
        Manual Analysis
      </h4>

      <textarea
        ref={textareaRef}
        className="w-full h-32 p-3 border border-orange-300 rounded text-sm mb-2 focus:outline-none focus:ring-2 focus:ring-orange-300"
        placeholder="Enter your manual analysis or feedback..."
        value={feedbackText}
        onChange={(e) => onFeedbackChange(e.target.value)}
        onBlur={handleBlur}
      />
    </div>
  );
}
