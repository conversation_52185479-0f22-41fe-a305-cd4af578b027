import React, { Fragment, useState } from "react";
import { Check, X, ChevronUp, ChevronRight } from "lucide-react";

interface DecisionSupportItem {
  label: string;
  value: string;
}

interface ActionItemProps {
  id: number;
  title: string;
  status: string;
  decisionSupport?: DecisionSupportItem[];
  isApproved: boolean;
  isRejected: boolean;
  isDecisionSupportExpanded: boolean;
  onToggleDecisionSupport: (id: number) => void;
  onApprove: (id: number) => void;
  onReject: (id: number) => void;
}

export function ActionItem({
  id,
  title,
  status,
  decisionSupport = [],
  isApproved,
  isRejected,
  isDecisionSupportExpanded,
  onToggleDecisionSupport,
  onApprove,
  onReject,
}: ActionItemProps) {
  return (
    <div
      className={`${
        isApproved
          ? "bg-green-50 border-green-200"
          : isRejected
          ? "bg-red-50 border-red-200"
          : "bg-gray-50 border-gray-200"
      } p-3 rounded border`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1 flex items-center">
          <div
            className={`w-4 h-4 rounded-full mr-2 flex-shrink-0 ${
              isApproved
                ? "bg-green-500"
                : isRejected
                ? "bg-red-500"
                : status === "green"
                ? "bg-green-500"
                : status === "yellow"
                ? "bg-yellow-500"
                : "bg-red-500"
            }`}
          ></div>
          <p className="font-medium">{title}</p>
        </div>
        <div className="flex space-x-1">
          <button
            className={`p-1 ${isApproved ? "bg-green-500 text-white" : "bg-green-100 text-green-600"} rounded-full`}
            onClick={(e) => {
              e.stopPropagation();
              onApprove(id);
            }}
            title={isApproved ? "Already approved" : "Approve"}
          >
            <Check size={16} />
          </button>
          <button
            className={`p-1 ${isRejected ? "bg-red-500 text-white" : "bg-red-100 text-red-600"} rounded-full`}
            onClick={(e) => {
              e.stopPropagation();
              onReject(id);
            }}
            title={isRejected ? "Already rejected" : "Reject"}
          >
            <X size={16} />
          </button>
          <button
            className="p-1 bg-blue-50 rounded-full text-blue-600"
            onClick={(e) => {
              e.stopPropagation();
              onToggleDecisionSupport(id);
            }}
          >
            {isDecisionSupportExpanded ? (
              <ChevronUp size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
          </button>
        </div>
      </div>

      {isDecisionSupportExpanded && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-medium mb-2">Decision Support</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {decisionSupport.map((support, idx) => (
              <Fragment key={idx}>
                <div className="text-gray-600">{support.label}</div>
                <div className="font-medium">{support.value}</div>
              </Fragment>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
