export function NotificationsPanel() {
  return (
    <div className="panel-content">
      <p className="mb-4">
        This panel displays notifications and alerts related to claims and system updates.
      </p>
      <div className="panel-section">
        <h3>New Notifications <span className="sidebar-badge ml-2">3</span></h3>
        <ul>
          <li className="flex justify-between items-center">
            <div>
              <div className="font-medium text-gray-800">New claim assigned</div>
              <div className="text-sm text-gray-600">Claim #4567 has been assigned to you</div>
              <div className="text-xs text-gray-500">2 minutes ago</div>
            </div>
            <button className="text-xs px-2 py-1">Mark Read</button>
          </li>
          <li className="flex justify-between items-center">
            <div>
              <div className="font-medium text-gray-800">Document uploaded</div>
              <div className="text-sm text-gray-600">New document for Claim #1234</div>
              <div className="text-xs text-gray-500">1 hour ago</div>
            </div>
            <button className="text-xs px-2 py-1">Mark Read</button>
          </li>
          <li className="flex justify-between items-center">
            <div>
              <div className="font-medium text-gray-800">System update</div>
              <div className="text-sm text-gray-600">Claims system will be down for maintenance tonight</div>
              <div className="text-xs text-gray-500">3 hours ago</div>
            </div>
            <button className="text-xs px-2 py-1">Mark Read</button>
          </li>
        </ul>
      </div>
      <div className="panel-section">
        <h3>Earlier Notifications</h3>
        <ul>
          <li className="text-gray-500">
            <div className="font-medium">Claim status updated</div>
            <div className="text-sm">Claim #9876 status changed to "Approved"</div>
            <div className="text-xs">Yesterday</div>
          </li>
          <li className="text-gray-500">
            <div className="font-medium">New policy update</div>
            <div className="text-sm">Critical illness claim guidelines updated</div>
            <div className="text-xs">2 days ago</div>
          </li>
        </ul>
      </div>
      <div className="panel-section">
        <button className="w-full">Mark All as Read</button>
      </div>
    </div>
  );
}
