"use client";

import { useState, useRef, useEffect } from "react";
import { Loader2, Info, X } from "lucide-react";
import { Source, ContextSearchDocument } from "@/lib/x-stream-faq";
import { toast } from "sonner";

// Define types for chat messages
interface ChatMessage {
  id: string;
  sender: "bot" | "user";
  text: string;
  timestamp: Date;
  sources?: Source[];
  contextDocuments?: ContextSearchDocument[];
}

export function ClaimChatBotPanel() {
  // State for chat messages
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "welcome",
      sender: "bot",
      text: "Hello! How can I assist you with claims today?",
      timestamp: new Date(),
    },
  ]);

  // State for user input
  const [input, setInput] = useState("");

  // State for loading
  const [isLoading, setIsLoading] = useState(false);

  // State for showing sources
  const [showSources, setShowSources] = useState(false);

  // State for current sources being viewed
  const [currentSources, setCurrentSources] = useState<{
    sources: Source[];
    documents: ContextSearchDocument[];
  } | null>(null);

  // Ref for chat container to auto-scroll
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Function to handle sending a message
  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: "user",
      text: input,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      // Call the API
      const response = await fetch("/api/x-stream-faq/answer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query: input }),
      });

      if (!response.ok) {
        throw new Error("Failed to get answer from API");
      }

      const data = await response.json();

      // Add bot message to chat
      const botMessage: ChatMessage = {
        id: data.id || Date.now().toString() + "-bot",
        sender: "bot",
        text: data.response,
        timestamp: new Date(),
        sources: data.sources,
        contextDocuments: data.contextSearchDocuments,
      };

      setMessages((prev) => [...prev, botMessage]);
    } catch (error) {
      console.error("Error getting answer:", error);
      toast.error("Failed to get an answer. Please try again.");

      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: Date.now().toString() + "-error",
        sender: "bot",
        text: "I'm sorry, I couldn't process your question. Please try again.",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle showing sources for a message
  const handleShowSources = (message: ChatMessage) => {
    if (message.sources && message.contextDocuments) {
      setCurrentSources({
        sources: message.sources,
        documents: message.contextDocuments,
      });
      setShowSources(true);
    }
  };

  // Function to handle suggested questions
  const handleSuggestedQuestion = (question: string) => {
    setInput(question);
  };

  return (
    <div className="panel-content">
      <p className="mb-4 ml-2">
        This panel provides an AI-powered chatbot to assist with claim
        processing and answering questions.
      </p>
      <div className="panel-section">
        <h3>Chat Interface</h3>
        <div
          ref={chatContainerRef}
          className="bg-gray-50 p-3 rounded border border-gray-200 mb-3 max-h-60 overflow-y-auto"
        >
          {messages.map((message) => (
            <div key={message.id} className="mb-2">
              <div className="text-xs text-gray-500 mb-1 flex justify-between">
                <span>{message.sender === "bot" ? "ClaimBot" : "You"}</span>
                {message.sender === "bot" &&
                  message.sources &&
                  message.sources.length > 0 && (
                    <button
                      onClick={() => handleShowSources(message)}
                      className="text-blue-500 hover:text-blue-700 text-xs flex items-center"
                    >
                      <Info size={12} className="mr-1" /> View Sources
                    </button>
                  )}
              </div>
              <div
                className={`${
                  message.sender === "bot" ? "bg-gray-100" : "bg-blue-50"
                } p-2 rounded-lg`}
              >
                {message.text}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-gray-500">Thinking...</span>
            </div>
          )}
        </div>
        <div className="flex">
          <input
            type="text"
            placeholder="Type your question here..."
            className="flex-1 mr-2"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !isLoading) {
                handleSendMessage();
              }
            }}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !input.trim()}
            className="disabled:opacity-50"
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Send"}
          </button>
        </div>
      </div>

      {/* Sources Modal */}
      {showSources && currentSources && (
        <div
          className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
          onClick={() => setShowSources(false)}
        >
          <div
            className="bg-white rounded-lg shadow-lg w-3/4 max-w-3xl max-h-[80vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="font-semibold">Source Documents</h3>
              <button
                onClick={() => setShowSources(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-8rem)]">
              {currentSources.sources.map((source, index) => (
                <div
                  key={index}
                  className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{source.parentDocName}</p>
                      {source.title && (
                        <p className="text-sm text-gray-600">{source.title}</p>
                      )}
                    </div>
                    <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                      Relevance: {Math.round(source.importance * 100)}%
                    </span>
                  </div>
                  <div className="mt-2 text-sm border-t border-gray-200 pt-2">
                    {source.content}
                  </div>
                </div>
              ))}

              {currentSources.documents.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Referenced Documents:</h4>
                  <ul className="list-disc list-inside">
                    {currentSources.documents.map((doc, index) => (
                      <li key={index} className="text-sm">
                        {doc.name} {doc.title && `- ${doc.title}`}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="panel-section">
        <h3>Suggested Questions</h3>
        <ul className="text-gray-700">
          <li>
            <button
              onClick={() =>
                handleSuggestedQuestion(
                  "What documents are required for a critical illness claim?",
                )
              }
              className="text-left hover:text-blue-600 w-full"
            >
              What documents are required for a critical illness claim?
            </button>
          </li>
          <li>
            <button
              onClick={() =>
                handleSuggestedQuestion(
                  "What is the name on the Cardiac Enzyme Test Report?",
                )
              }
              className="text-left hover:text-blue-600 w-full"
            >
              What is the name on the Cardiac Enzyme Test Report?
            </button>
          </li>
          <li>
            <button
              onClick={() =>
                handleSuggestedQuestion(
                  "What is the diagnosis in the medical records?",
                )
              }
              className="text-left hover:text-blue-600 w-full"
            >
              What is the diagnosis in the medical records?
            </button>
          </li>
        </ul>
      </div>
    </div>
  );
}
