"use client";

import { useState, useRef, useEffect } from "react";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  UserCog,
  MessageSquareText,
  BookOpen,
  Bell,
  FileText,
} from "lucide-react";
import { ClaimExaminerPanel } from "./panels/claim-examiner-panel";
import { ClaimChatBotPanel } from "./panels/claim-chatbot-panel";
import { KnowledgePanel } from "./panels/knowledge-panel";
import { NotificationsPanel } from "./panels/notifications-panel";
import { SummaryPanel } from "./panels/summary-panel";

type SidebarItem = {
  id: string;
  icon: React.ReactNode;
  label: string;
  panel: React.ReactNode;
};

export function DetailSidebar() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [panelWidth, setPanelWidth] = useState<number>(35); // Default width percentage
  const [isResizing, setIsResizing] = useState(false);
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  const initialX = useRef<number>(0);
  const initialWidth = useRef<number>(0);
  const pathname = usePathname();

  // Constants for min/max width
  const MIN_WIDTH_PERCENT = 20;
  const MAX_WIDTH_PERCENT = 85;

  // Determine the current context (claims or loans)
  const isClaimsContext = pathname?.includes("/claims-management/");
  const isLoansContext = pathname?.includes("/loan-management/");

  const sidebarItems: SidebarItem[] = [
    {
      id: "claim-examiner",
      icon: <UserCog size={24} />,
      label: isLoansContext ? "Loan Examiner" : "Claim Examiner",
      panel: <ClaimExaminerPanel />,
    },
    {
      id: "claim-chatbot",
      icon: <MessageSquareText size={24} />,
      label: isLoansContext ? "Loan ChatBot" : "Claim ChatBot",
      panel: <ClaimChatBotPanel />,
    },
    {
      id: "knowledge",
      icon: <BookOpen size={24} />,
      label: "Knowledge",
      panel: <KnowledgePanel />,
    },
    {
      id: "notifications",
      icon: <Bell size={24} />,
      label: "Notifications / Alerts",
      panel: <NotificationsPanel />,
    },
    {
      id: "summary",
      icon: <FileText size={24} />,
      label: "Summary",
      panel: <SummaryPanel />,
    },
  ];

  const handleItemClick = (itemId: string) => {
    if (activeItem === itemId) {
      // If clicking the active item, collapse the sidebar
      setIsExpanded(false);
      setActiveItem(null);
    } else {
      // Otherwise, expand the sidebar and set the active item
      setIsExpanded(true);
      setActiveItem(itemId);
    }
  };

  // Handle mouse down on resize handle - simplified for reliability
  const handleMouseDown = (e: React.MouseEvent) => {
    console.log("Mouse down on resize handle");
    e.preventDefault();
    e.stopPropagation();

    // Set resizing state
    setIsResizing(true);

    // We don't need to track initial values anymore with our new calculation approach

    // Add a class to the body to indicate resizing is happening
    document.body.classList.add("resizing");
    document.body.style.cursor = "ew-resize";

    // Add event listeners for mouse move and mouse up
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    // Also listen for mouseout/leave events on the window
    document.addEventListener("mouseleave", handleMouseUp);
  };

  // Handle mouse move during resize - completely rewritten for simplicity and reliability
  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;

    // Get the current mouse position
    const mouseX = e.clientX;

    // Get the window width
    const windowWidth = window.innerWidth;

    // Calculate the distance from the right edge of the window
    // The panel is positioned from the right, so we need to calculate from there
    const distanceFromRight = windowWidth - mouseX - 64; // 64px is the width of the sidebar

    // Convert to percentage of window width
    const widthPercent = (distanceFromRight / windowWidth) * 100;

    // Constrain to min/max width
    const newWidth = Math.max(
      MIN_WIDTH_PERCENT,
      Math.min(MAX_WIDTH_PERCENT, widthPercent),
    );

    // Debug to console
    console.log(
      `Mouse X: ${mouseX}, Window Width: ${windowWidth}, Distance from right: ${distanceFromRight}, New Width: ${newWidth}%`,
    );

    // Update the panel width
    setPanelWidth(newWidth);
  };

  // Handle mouse up to end resize
  const handleMouseUp = () => {
    console.log("Mouse up - ending resize");

    if (!isResizing) return; // Only proceed if we were actually resizing

    setIsResizing(false);

    // Remove the resizing class and reset cursor
    document.body.classList.remove("resizing");
    document.body.style.cursor = "";

    // Clean up all event listeners
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
    document.removeEventListener("mouseleave", handleMouseUp);
  };

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      // Clean up all event listeners
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("mouseleave", handleMouseUp);

      // Also clean up any lingering classes or styles
      document.body.classList.remove("resizing");
      document.body.style.cursor = "";
    };
  }, []);

  return (
    <div className="examiner-theme h-full">
      {/* Expandable panel area - positioned to the left of the icons */}
      <div
        className={cn(
          "sidebar-panel fixed top-0 bottom-0 overflow-hidden transition-all duration-300 z-50",
          "bg-[var(--sidebar-panel-bg)]",
          isResizing ? "transition-none resizing" : "", // Disable transition during resize for better performance
        )}
        style={{
          width: isExpanded ? `${panelWidth}%` : "0",
          right: "64px", // Always positioned to the left of the icons
          boxShadow: isExpanded
            ? "-2px 0 10px var(--sidebar-panel-shadow)"
            : "none",
        }}
      >
        {/* Resize handle - simplified for better reliability */}
        {isExpanded && (
          <div
            ref={resizeHandleRef}
            className="resize-handle absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize bg-gray-100 hover:bg-[var(--sidebar-accent)] hover:opacity-50 z-50 flex items-center justify-center"
            onMouseDown={handleMouseDown}
            title="Drag to resize panel"
          >
            <div className="text-gray-400 select-none">⋮</div>
          </div>
        )}

        {sidebarItems.map((item) => (
          <div
            key={item.id}
            className={cn(
              "h-full p-6 pl-8 overflow-auto", // Added left padding to avoid overlap with resize handle
              activeItem === item.id ? "block" : "hidden",
            )}
          >
            <h2 className="text-xl font-semibold mb-6 text-[var(--sidebar-panel-heading)] border-b border-[var(--sidebar-panel-border)] pb-2">
              {item.label}
            </h2>
            {item.panel}
          </div>
        ))}
      </div>

      {/* Main sidebar with icons - always visible on the right */}
      <div
        className={cn(
          "sidebar fixed top-0 bottom-0 right-0 flex flex-col items-center py-6 transition-all duration-300 z-40",
          "bg-[var(--sidebar-bg)]",
        )}
        style={{ width: "64px" }}
      >
        {/* logo */}
        <img
          src="/uniphore-x.svg"
          alt="logo"
          className="w-12 h-12 flex items-center justify-center"
        />

        <div className="flex flex-col items-center space-y-0 mt-10 w-full">
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleItemClick(item.id)}
              className={cn(
                "sidebar-icon w-full h-16 flex items-center justify-center transition-all",
                activeItem === item.id
                  ? "active bg-[var(--sidebar-active)] text-white"
                  : "text-white hover:bg-[var(--sidebar-hover)]",
              )}
              title={item.label}
            >
              {item.icon}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
