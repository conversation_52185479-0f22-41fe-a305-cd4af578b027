"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

interface ClaimStatusFormProps {
  claimId: number;
  currentStatus: string;
  onStatusChange: (claimId: number, newStatus: string) => Promise<void>;
}

export function ClaimStatusForm({ claimId, currentStatus, onStatusChange }: ClaimStatusFormProps) {
  const [status, setStatus] = useState<string>(currentStatus);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusChange = async () => {
    if (status === currentStatus) return;
    
    setIsUpdating(true);
    try {
      await onStatusChange(claimId, status);
      toast.success(`Claim #${claimId} status updated to ${status}`);
    } catch (error) {
      console.error("Error updating claim status:", error);
      toast.error("Failed to update claim status");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="flex items-end gap-4">
      <div className="flex-1">
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="submitted">Submitted</SelectItem>
            <SelectItem value="in review">In Review</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <Button 
        onClick={handleStatusChange} 
        disabled={isUpdating || status === currentStatus}
      >
        {isUpdating ? "Updating..." : "Update Status"}
      </Button>
    </div>
  );
}
