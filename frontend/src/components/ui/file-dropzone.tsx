import { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { validateFiles, FileValidationOptions } from "@/lib/utils";

interface FileDropzoneProps {
  onChange: (files: File[]) => void;
  files: File[];
  accept?: string;
  maxFiles?: number;
  onError?: (errors: string[]) => void;
  validationOptions?: FileValidationOptions;
}

export function FileDropzone({ onChange, files, accept, maxFiles, onError, validationOptions }: FileDropzoneProps) {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const newFiles = [...files, ...acceptedFiles];
      const errors = validateFiles(newFiles, { ...validationOptions, maxFiles });
      if (errors.length > 0) {
        if (onError) onError(errors);
        return;
      }
      onChange(maxFiles ? newFiles.slice(0, maxFiles) : newFiles);
      if (onError) onError([]);
    },
    [files, onChange, maxFiles, onError, validationOptions]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept ? accept.split(',').reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>) : undefined,
    multiple: true,
    maxFiles,
  });

  const removeFile = (index: number) => {
    const newFiles = files.slice();
    newFiles.splice(index, 1);
    onChange(newFiles);
  };

  return (
    <Card className="p-0 border-dashed border-2 border-gray-300 bg-gray-50">
      <CardContent className="p-4">
        <div
          {...getRootProps()}
          className={`flex flex-col items-center justify-center cursor-pointer py-8 px-4 rounded transition border-2 border-dashed border-gray-300 bg-white hover:bg-gray-100 ${
            isDragActive ? "bg-blue-50 border-blue-400" : ""
          }`}
        >
          <input {...getInputProps()} />
          <p className="text-gray-500 text-center">
            {isDragActive
              ? "Drop the files here ..."
              : "Drag & drop files here, or click to select files"}
          </p>
        </div>
        {files.length > 0 && (
          <ul className="mt-4 space-y-2">
            {files.map((file, idx) => (
              <li key={file.name + idx} className="flex items-center justify-between bg-gray-100 rounded px-3 py-2">
                <span className="truncate max-w-xs text-sm">{file.name}</span>
                <Button type="button" variant="ghost" size="sm" onClick={() => removeFile(idx)}>
                  Remove
                </Button>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}