"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterP<PERSON> } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="top-right"
      closeButton
      richColors
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
          "--success-bg": "#159e56", // Primary color from management theme
          "--success-text": "white",
          "--success-border": "#159e56",
          "--error-bg": "#e11d48", // Red color
          "--error-text": "white",
          "--error-border": "#e11d48",
        } as React.CSSProperties
      }
      toastOptions={{
        duration: 4000,
        className: "!bg-opacity-100 !border-2", // Make sure background is fully opaque
      }}
      {...props}
    />
  )
}

export { Toaster }
