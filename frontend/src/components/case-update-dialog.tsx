"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Import for RPA simulation
import { simulateRpaAction } from "@/lib/rpa-simulator";

interface CaseUpdateDialogProps {
  claimId: number;
  summary: string;
  onClose: () => void;
  onSubmit: (summary: string) => Promise<void>;
}

export function CaseUpdateDialog({
  claimId,
  summary,
  onClose,
  onSubmit,
}: CaseUpdateDialogProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedSummary, setEditedSummary] = useState(summary);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Show the dialog with animation when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);
    return () => clearTimeout(timer);
  }, []);

  // Handle the submit button click
  const handleSubmit = async () => {
    if (!editedSummary.trim()) {
      toast.error("Summary cannot be empty");
      return;
    }

    setIsSubmitting(true);
    try {
      // First, submit the case update as normal
      await onSubmit(editedSummary);
      toast.success("Case update submitted successfully");

      // Then, simulate RPA to create a comment with the summary
      try {
        // We'll close the dialog first to avoid UI conflicts
        handleClose();

        // Wait a moment for the dialog to close before starting RPA
        setTimeout(async () => {
          console.log("Starting RPA simulation with summary:", editedSummary);
          // Simulate RPA action to create a comment with the summary
          await simulateRpaAction(editedSummary);
          toast.success("RPA simulation completed: Comment added automatically");
        }, 500);
      } catch (rpaError) {
        console.error("RPA simulation error:", rpaError);
        toast.error("RPA simulation failed");
        // Still close the dialog even if RPA fails
        handleClose();
      }
    } catch (error) {
      console.error("Error submitting case update:", error);
      toast.error("Failed to submit case update");
      setIsSubmitting(false);
    }
  };

  // Handle the close button click with animation
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  return (
    <div className="fixed inset-0 pointer-events-none flex justify-end z-50">
      {/* Invisible overlay to capture clicks outside the modal */}
      <div
        className="absolute inset-0 pointer-events-auto bg-black/20"
        onClick={handleClose}
      />
      <div
        className="w-[35%] h-full transform transition-transform duration-300 ease-in-out pointer-events-auto"
        style={{
          transform: isVisible ? "translateX(0)" : "translateX(100%)",
          boxShadow: "-4px 0 15px rgba(0, 0, 0, 0.1)",
          right: "64px", // Match the sidebar panel position
        }}
        ref={modalRef}
      >
        <Card className="h-full w-full flex flex-col rounded-l-xl rounded-r-none bg-[var(--sidebar-panel-bg)] py-0">
          <CardHeader className="flex flex-row items-center justify-between bg-[var(--sidebar-panel-bg)] border-b py-2 px-4">
            <CardTitle className="text-[var(--sidebar-panel-text)] font-semibold">
              Case Update - Claim #{claimId}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={handleClose} className="text-[var(--sidebar-panel-text)]">
              ×
            </Button>
          </CardHeader>
          <CardContent className="flex-1 overflow-auto p-4 gap-0 text-[var(--sidebar-panel-text)]">
            <div className="mb-4">
              <p className="text-sm mb-2">
                Review the case summary below. You can edit it if needed before
                submitting.
              </p>
            </div>

            {isEditing ? (
              <Textarea
                className="w-full h-64 p-3 border border-gray-300 rounded text-sm mb-4 bg-white text-gray-800"
                value={editedSummary}
                onChange={(e) => setEditedSummary(e.target.value)}
                placeholder="Enter case summary..."
              />
            ) : (
              <div className="bg-white/10 p-3 rounded border border-gray-300 mb-4 text-sm overflow-auto">
                <div className="prose prose-sm max-w-none prose-headings:text-[var(--sidebar-panel-text)] prose-p:text-[var(--sidebar-panel-text)] prose-strong:text-[var(--sidebar-panel-text)] prose-li:text-[var(--sidebar-panel-text)]">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {editedSummary}
                  </ReactMarkdown>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 mt-4">
              <Button
                variant="outline"
                onClick={handleClose}
                className="border-gray-400 text-[var(--sidebar-panel-text)]"
              >
                Cancel
              </Button>
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditing(false);
                      setEditedSummary(summary); // Reset to original
                    }}
                    className="border-gray-400 text-[var(--sidebar-panel-text)]"
                  >
                    Reset
                  </Button>
                  <Button
                    onClick={() => {
                      setIsEditing(false);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Done Editing
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  className="border-gray-400 text-[var(--sidebar-panel-text)]"
                >
                  Edit
                </Button>
              )}
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
