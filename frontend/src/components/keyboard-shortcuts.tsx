"use client";

import { useEffect, useState } from "react";

export function KeyboardShortcuts() {
  const [mounted, setMounted] = useState(false);

  // Handle client-side hydration
  useEffect(() => {
    setMounted(true);
    console.log("KeyboardShortcuts component mounted");
  }, []);

  // This component is kept for future keyboard shortcuts
  // The Ctrl+T toggle functionality has been removed as we now only use the enhanced view

  // This component doesn't render anything
  return null;
}
