import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/documents:
 *   get:
 *     summary: Get all claim documents
 *     description: Returns a list of all claim documents
 *     tags:
 *       - Claim Documents
 *     responses:
 *       200:
 *         description: List of claim documents
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ClaimDocument'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/documents - list all documents
export async function GET(/* _req: NextRequest */) {
  try {
    const documents = await prisma.claimDocument.findMany({ include: { claim: true } });
    return NextResponse.json(documents);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/documents:
 *   post:
 *     summary: Create a new claim document
 *     description: Creates a new document and associates it with a claim
 *     tags:
 *       - Claim Documents
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               claimId:
 *                 type: integer
 *                 description: ID of the claim
 *               fileName:
 *                 type: string
 *                 description: Name of the file
 *               filePath:
 *                 type: string
 *                 description: Path to the file on the server
 *               uploadedAt:
 *                 type: string
 *                 format: date-time
 *                 description: Date and time when the document was uploaded
 *             required:
 *               - claimId
 *               - fileName
 *               - filePath
 *     responses:
 *       201:
 *         description: Document created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ClaimDocument'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/documents - create a new document
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { claimId, fileName, filePath, uploadedAt } = body;
    if (!claimId || !fileName || !filePath) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    const document = await prisma.claimDocument.create({
      data: {
        claimId,
        fileName,
        filePath,
        uploadedAt: uploadedAt ? new Date(uploadedAt) : undefined,
      },
      include: { claim: true },
    });
    return NextResponse.json(document, { status: 201 });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}