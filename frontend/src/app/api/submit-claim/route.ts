import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/submit-claim:
 *   post:
 *     summary: Submit a new claim
 *     description: Creates a new claim with employee information and uploaded files
 *     tags:
 *       - Claim Submission
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: First name of the employee
 *               lastName:
 *                 type: string
 *                 description: Last name of the employee
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the employee
 *               phone:
 *                 type: string
 *                 description: Phone number of the employee
 *               address:
 *                 type: string
 *                 description: Address of the employee
 *               employerName:
 *                 type: string
 *                 description: Name of the employer
 *               groupId:
 *                 type: string
 *                 description: Group ID of the employee
 *               memberId:
 *                 type: string
 *                 description: Member ID of the employee
 *               claimType:
 *                 type: string
 *                 description: Type of claim (defaults to "Critical Illness")
 *               description:
 *                 type: string
 *                 description: Description of the claim
 *               incidentDate:
 *                 type: string
 *                 format: date-time
 *                 description: Date of the incident
 *               files:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     originalname:
 *                       type: string
 *                     path:
 *                       type: string
 *                 description: Array of uploaded files
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *               - description
 *               - files
 *     responses:
 *       200:
 *         description: Claim submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 claim:
 *                   $ref: '#/components/schemas/Claim'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      employerName,
      groupId,
      memberId,
      claimType,
      description,
      incidentDate,
      files
    } = body;

    if (!firstName || !lastName || !email || !description || !files || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Find or create employee
    let employee = await prisma.employee.findUnique({ where: { email } });
    if (!employee) {
      employee = await prisma.employee.create({
        data: {
          firstName,
          lastName,
          email,
          phone,
          address,
          employerName,
          groupId,
          memberId
        }
      });
    } else {
      // Update employee information if it exists
      employee = await prisma.employee.update({
        where: { id: employee.id },
        data: {
          firstName,
          lastName,
          phone,
          address,
          employerName,
          groupId,
          memberId
        }
      });
    }

    // Create claim
    const claim = await prisma.claim.create({
      data: {
        employeeId: employee.id,
        claimType: claimType || "Critical Illness",
        description,
        incidentDate: incidentDate ? new Date(incidentDate) : null,
        status: "submitted",
        documents: {
          create: files.map((file: { originalname: string; path: string }) => ({
            fileName: file.originalname,
            filePath: file.path,
          })),
        },
      },
      include: { documents: true },
    });

    // Notify external webhook about the new claim
    try {
      const webhookUrl = `https://n8n-uniphore.fschaefer-next-cloud.synology.me/webhook/create-claim?claimId=${claim.id}`;
      const webhookResponse = await fetch(webhookUrl, {
        method: 'GET',
        headers: {
          'api-key': 'NkrZPco7C1tVn6'
        }
      });

      if (!webhookResponse.ok) {
        console.error('Webhook notification failed:', await webhookResponse.text());
      }
    } catch (webhookError) {
      // Log the error but don't fail the claim submission
      console.error('Error notifying webhook:', webhookError);
    }

    return NextResponse.json({ success: true, claim });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}