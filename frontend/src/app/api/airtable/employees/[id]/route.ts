import { NextRequest, NextResponse } from "next/server";
import { fetchRecord, updateRecord, deleteRecord } from "@/lib/airtable-sdk";
import { AirtableEmployee, AirtableTable } from "@/types/airtable";

/**
 * @swagger
 * /api/airtable/employees/{id}:
 *   get:
 *     summary: Get an employee by ID from Airtable
 *     description: Retrieve an employee by its ID from Airtable
 *     tags:
 *       - Airtable
 *       - Employees
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID in Airtable
 *     responses:
 *       200:
 *         description: Employee details
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 *   patch:
 *     summary: Update an employee in Airtable
 *     description: Updates an existing employee record in Airtable
 *     tags:
 *       - Airtable
 *       - Employees
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID in Airtable
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               employerName:
 *                 type: string
 *               groupId:
 *                 type: string
 *               memberId:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Employee updated
 *       400:
 *         description: Bad request
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 *   delete:
 *     summary: Delete an employee from Airtable
 *     description: Deletes an employee record from Airtable
 *     tags:
 *       - Airtable
 *       - Employees
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID in Airtable
 *     responses:
 *       200:
 *         description: Employee deleted
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 */

// GET /api/airtable/employees/[id] - get employee by ID from Airtable
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;

    const response = await fetchRecord(
      AirtableTable.EMPLOYEES,
      id
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching employee from Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Employee not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch employee from Airtable" },
      { status: 500 }
    );
  }
}

// PATCH /api/airtable/employees/[id] - update employee in Airtable
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;
    const body = await req.json();

    // Update employee record in Airtable
    const response = await updateRecord(
      AirtableTable.EMPLOYEES,
      id,
      body as any
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating employee in Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Employee not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update employee in Airtable" },
      { status: 500 }
    );
  }
}

// DELETE /api/airtable/employees/[id] - delete employee from Airtable
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;

    const response = await deleteRecord(
      AirtableTable.EMPLOYEES,
      id
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting employee from Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Employee not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete employee from Airtable" },
      { status: 500 }
    );
  }
}
