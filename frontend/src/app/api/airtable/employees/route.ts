import { NextRequest, NextResponse } from "next/server";
import { fetchRecords, createRecord } from "@/lib/airtable-sdk";
import { AirtableEmployee, AirtableTable } from "@/types/airtable";

/**
 * @swagger
 * /api/airtable/employees:
 *   get:
 *     summary: Get all employees from Airtable
 *     description: Returns a list of all employees from Airtable
 *     tags:
 *       - Airtable
 *       - Employees
 *     responses:
 *       200:
 *         description: List of employees
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new employee in Airtable
 *     description: Creates a new employee record in Airtable
 *     tags:
 *       - Airtable
 *       - Employees
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               employerName:
 *                 type: string
 *               groupId:
 *                 type: string
 *               memberId:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Employee created
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */

// GET /api/airtable/employees - list all employees from Airtable
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const maxRecords = url.searchParams.get('maxRecords');
    const view = url.searchParams.get('view');
    const filterByFormula = url.searchParams.get('filterByFormula');

    const options: any = {};
    if (maxRecords) options.maxRecords = parseInt(maxRecords);
    if (view) options.view = view;
    if (filterByFormula) options.filterByFormula = filterByFormula;

    const response = await fetchRecords(
      AirtableTable.EMPLOYEES,
      options
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching employees from Airtable:", error);
    return NextResponse.json(
      { error: "Failed to fetch employees from Airtable" },
      { status: 500 }
    );
  }
}

// POST /api/airtable/employees - create a new employee in Airtable
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      employerName,
      groupId,
      memberId,
      notes
    } = body;

    // Validate required fields
    if (!firstName || !lastName || !email) {
      return NextResponse.json(
        { error: "Missing required fields: firstName, lastName, email" },
        { status: 400 }
      );
    }

    // Create employee record in Airtable
    const employee: Partial<AirtableEmployee> = {
      firstName,
      lastName,
      email,
      phone,
      address,
      employerName,
      groupId,
      memberId,
      notes
    };

    const response = await createRecord(
      AirtableTable.EMPLOYEES,
      employee as any
    );

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating employee in Airtable:", error);
    return NextResponse.json(
      { error: "Failed to create employee in Airtable" },
      { status: 500 }
    );
  }
}
