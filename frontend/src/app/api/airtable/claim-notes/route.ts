import { NextRequest, NextResponse } from "next/server";
import { createRecord, fetchRecords } from "@/lib/airtable-sdk";
import { AirtableClaimNote, AirtableTable } from "@/types/airtable";

/**
 * @swagger
 * /api/airtable/claim-notes:
 *   get:
 *     summary: Get all claim notes from Airtable
 *     description: Returns a list of all claim notes from Airtable
 *     tags:
 *       - Airtable
 *       - Claim Notes
 *     parameters:
 *       - in: query
 *         name: claimId
 *         schema:
 *           type: number
 *         description: Filter notes by claim ID
 *     responses:
 *       200:
 *         description: List of claim notes
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new claim note in Airtable
 *     description: Creates a new claim note record in Airtable
 *     tags:
 *       - Airtable
 *       - Claim Notes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               claimId:
 *                 type: number
 *                 description: The claim ID
 *               notes:
 *                 type: string
 *                 description: The note text
 *               createdBy:
 *                 type: string
 *                 enum: [system, user]
 *                 description: Who created the note
 *               noteType:
 *                 type: string
 *                 enum: [Process, User, Feedback, NaN]
 *                 description: The type of note
 *     responses:
 *       201:
 *         description: Claim note created
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */

// GET /api/airtable/claim-notes - list all claim notes from Airtable
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const claimId = url.searchParams.get("claimId");

    const options: any = {};

    // Filter by claim ID if provided
    if (claimId) {
      options.filterByFormula = `{Claim ID} = ${claimId}`;
    }

    const response = await fetchRecords(AirtableTable.CLAIM_NOTES, options);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching claim notes from Airtable:", error);
    return NextResponse.json(
      { error: "Failed to fetch claim notes from Airtable" },
      { status: 500 },
    );
  }
}

// POST /api/airtable/claim-notes - create a new claim note in Airtable
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { claimId, notes, createdBy, noteType } = body;

    // Validate required fields
    if (!claimId || !notes || !createdBy || !noteType) {
      return NextResponse.json(
        {
          error: "Missing required fields: claimId, notes, createdBy, noteType",
        },
        { status: 400 },
      );
    }

    // Validate createdBy field
    if (createdBy !== "system" && createdBy !== "user") {
      return NextResponse.json(
        { error: "createdBy must be either 'system' or 'user'" },
        { status: 400 },
      );
    }

    // Validate noteType field
    if (!["Process", "User", "Feedback", "NaN"].includes(noteType)) {
      return NextResponse.json(
        {
          error:
            "noteType must be one of: 'Process', 'User', 'Feedback', 'NaN'",
        },
        { status: 400 },
      );
    }

    // Create claim note record in Airtable
    const claimNote: Partial<AirtableClaimNote> = {
      "Claim ID": claimId,
      Notes: notes,
      created_by: createdBy as "system" | "user",
      created_at: new Date().toISOString(),
      "Note Type": "Feedback" as "Process" | "User" | "Feedback" | "NaN",
    };

    console.log("claimNote", claimNote);

    const response = await createRecord(
      AirtableTable.CLAIM_NOTES,
      claimNote as any,
    );

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating claim note in Airtable:", error);
    return NextResponse.json(
      { error: "Failed to create claim note in Airtable" },
      { status: 500 },
    );
  }
}
