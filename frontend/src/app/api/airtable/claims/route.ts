import { NextRequest, NextResponse } from "next/server";
import { fetchRecords, createRecord } from "@/lib/airtable-sdk";
import { AirtableClaim, AirtableTable } from "@/types/airtable";

/**
 * @swagger
 * /api/airtable/claims:
 *   get:
 *     summary: Get all claims from Airtable
 *     description: Returns a list of all claims from Airtable
 *     tags:
 *       - Airtable
 *       - Claims
 *     responses:
 *       200:
 *         description: List of claims
 *       500:
 *         description: Server error
 *   post:
 *     summary: Create a new claim in Airtable
 *     description: Creates a new claim record in Airtable
 *     tags:
 *       - Airtable
 *       - Claims
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employeeId:
 *                 type: array
 *                 items:
 *                   type: string
 *               claimType:
 *                 type: string
 *               description:
 *                 type: string
 *               incidentDate:
 *                 type: string
 *                 format: date-time
 *               dateFiled:
 *                 type: string
 *                 format: date-time
 *               status:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Claim created
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */

// GET /api/airtable/claims - list all claims from Airtable
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const maxRecords = url.searchParams.get('maxRecords');
    const view = url.searchParams.get('view');
    const filterByFormula = url.searchParams.get('filterByFormula');

    const options: any = {};
    if (maxRecords) options.maxRecords = parseInt(maxRecords);
    if (view) options.view = view;
    if (filterByFormula) options.filterByFormula = filterByFormula;

    const response = await fetchRecords(
      AirtableTable.CLAIMS,
      options
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching claims from Airtable:", error);
    return NextResponse.json(
      { error: "Failed to fetch claims from Airtable" },
      { status: 500 }
    );
  }
}

// POST /api/airtable/claims - create a new claim in Airtable
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      employeeId,
      claimType,
      description,
      incidentDate,
      status,
      notes
    } = body;

    // Validate required fields
    if (!employeeId || !description || !status) {
      return NextResponse.json(
        { error: "Missing required fields: employeeId, description, status" },
        { status: 400 }
      );
    }

    // Create claim record in Airtable
    const claim: Partial<AirtableClaim> = {
      employeeId: Array.isArray(employeeId) ? employeeId : [employeeId],
      claimType: claimType || "Critical Illness",
      description,
      incidentDate,
      dateFiled: new Date().toISOString(),
      status,
      notes
    };

    const response = await createRecord(
      AirtableTable.CLAIMS,
      claim as any
    );

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating claim in Airtable:", error);
    return NextResponse.json(
      { error: "Failed to create claim in Airtable" },
      { status: 500 }
    );
  }
}
