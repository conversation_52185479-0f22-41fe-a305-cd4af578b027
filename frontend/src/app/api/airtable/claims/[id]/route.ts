import { NextRequest, NextResponse } from "next/server";
import { fetchRecord, updateRecord, deleteRecord } from "@/lib/airtable-sdk";
import { AirtableClaim, AirtableTable } from "@/types/airtable";

/**
 * @swagger
 * /api/airtable/claims/{id}:
 *   get:
 *     summary: Get a claim by ID from Airtable
 *     description: Retrieve a claim by its ID from Airtable
 *     tags:
 *       - Airtable
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID in Airtable
 *     responses:
 *       200:
 *         description: Claim details
 *       404:
 *         description: Claim not found
 *       500:
 *         description: Server error
 *   patch:
 *     summary: Update a claim in Airtable
 *     description: Updates an existing claim record in Airtable
 *     tags:
 *       - Airtable
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID in Airtable
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employeeId:
 *                 type: array
 *                 items:
 *                   type: string
 *               claimType:
 *                 type: string
 *               description:
 *                 type: string
 *               incidentDate:
 *                 type: string
 *                 format: date-time
 *               status:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Claim updated
 *       400:
 *         description: Bad request
 *       404:
 *         description: Claim not found
 *       500:
 *         description: Server error
 *   delete:
 *     summary: Delete a claim from Airtable
 *     description: Deletes a claim record from Airtable
 *     tags:
 *       - Airtable
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID in Airtable
 *     responses:
 *       200:
 *         description: Claim deleted
 *       404:
 *         description: Claim not found
 *       500:
 *         description: Server error
 */

// GET /api/airtable/claims/[id] - get claim by ID from Airtable
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;

    const response = await fetchRecord(
      AirtableTable.CLAIMS,
      id
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching claim from Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Claim not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch claim from Airtable" },
      { status: 500 }
    );
  }
}

// PATCH /api/airtable/claims/[id] - update claim in Airtable
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;
    const body = await req.json();

    // Ensure employeeId is an array if provided
    if (body.employeeId && !Array.isArray(body.employeeId)) {
      body.employeeId = [body.employeeId];
    }

    // Update claim record in Airtable
    const response = await updateRecord(
      AirtableTable.CLAIMS,
      id,
      body as any
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating claim in Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Claim not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update claim in Airtable" },
      { status: 500 }
    );
  }
}

// DELETE /api/airtable/claims/[id] - delete claim from Airtable
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;

    const response = await deleteRecord(
      AirtableTable.CLAIMS,
      id
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting claim from Airtable:", error);

    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: "Claim not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete claim from Airtable" },
      { status: 500 }
    );
  }
}
