import { NextRequest, NextResponse } from "next/server";
import { airtableConfig } from "@/lib/airtable-sdk";

/**
 * @swagger
 * /api/airtable:
 *   get:
 *     summary: Check Airtable API connection
 *     description: Verifies that the Airtable API is properly configured
 *     tags:
 *       - Airtable
 *     responses:
 *       200:
 *         description: Connection status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
export async function GET(/* _req: NextRequest */) {
  try {
    // Check if Airtable API key and base ID are configured
    if (!airtableConfig.apiKey) {
      return NextResponse.json(
        {
          status: "error",
          message: "Airtable API key is not configured"
        },
        { status: 500 }
      );
    }

    if (!airtableConfig.baseId) {
      return NextResponse.json(
        {
          status: "error",
          message: "Airtable base ID is not configured"
        },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      status: "success",
      message: "Airtable API is properly configured",
      baseId: airtableConfig.baseId,
    });
  } catch (error) {
    console.error("Error checking Airtable connection:", error);
    return NextResponse.json(
      { error: "Failed to check Airtable connection" },
      { status: 500 }
    );
  }
}
