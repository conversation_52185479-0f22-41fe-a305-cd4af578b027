import { NextRequest, NextResponse } from "next/server";
import { syncAllEmployeesToAirtable, syncAllClaimsToAirtable } from "@/lib/airtable-sync";

/**
 * @swagger
 * /api/airtable/sync:
 *   post:
 *     summary: Sync data between Prisma and Airtable
 *     description: Syncs all data from Prisma to Airtable
 *     tags:
 *       - Airtable
 *     responses:
 *       200:
 *         description: Sync completed successfully
 *       500:
 *         description: Server error
 */

// POST /api/airtable/sync - sync data between Prisma and Airtable
export async function POST(/* _req: NextRequest */) {
  try {
    // Sync employees first
    const employeeIdMap = await syncAllEmployeesToAirtable();
    
    // Then sync claims using the employee ID map
    await syncAllClaimsToAirtable(employeeIdMap);
    
    return NextResponse.json({
      status: "success",
      message: "Data synced successfully",
      employeesCount: employeeIdMap.size
    });
  } catch (error) {
    console.error("Error syncing data to Airtable:", error);
    return NextResponse.json(
      { error: "Failed to sync data to Airtable" },
      { status: 500 }
    );
  }
}
