import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/auto-loans:
 *   get:
 *     summary: Get all auto loan applications
 *     description: Returns a list of all auto loan applications with employee and document information
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by application status
 *       - in: query
 *         name: employeeId
 *         schema:
 *           type: integer
 *         description: Filter by employee ID
 *     responses:
 *       200:
 *         description: List of auto loan applications
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AutoLoan'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status");
    const employeeId = searchParams.get("employeeId");

    const where: any = {};
    if (status) where.status = status;
    if (employeeId) where.employeeId = parseInt(employeeId);

    const autoLoans = await prisma.autoLoan.findMany({
      where,
      include: {
        employee: true,
        documents: true,
      },
      orderBy: { applicationDate: "desc" },
    });

    return NextResponse.json(autoLoans);
  } catch (err) {
    console.error("Error fetching auto loans:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/auto-loans:
 *   post:
 *     summary: Create a new auto loan application
 *     description: Creates a new auto loan application
 *     tags:
 *       - Auto Loans
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employeeId:
 *                 type: integer
 *                 description: ID of the employee
 *               dniNumber:
 *                 type: string
 *                 description: DNI number
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Date of birth
 *               incomeSource:
 *                 type: string
 *                 description: Source of income
 *               loanAmount:
 *                 type: number
 *                 description: Loan amount
 *               totalPrice:
 *                 type: number
 *                 description: Total price
 *               tradeInValue:
 *                 type: number
 *                 description: Trade-in value
 *               downPayment:
 *                 type: number
 *                 description: Down payment
 *               remainingPrice:
 *                 type: number
 *                 description: Remaining price
 *               niv:
 *                 type: string
 *                 description: Vehicle identification number
 *               newPreowned:
 *                 type: string
 *                 description: New or preowned
 *               year:
 *                 type: integer
 *                 description: Vehicle year
 *               make:
 *                 type: string
 *                 description: Vehicle make
 *               model:
 *                 type: string
 *                 description: Vehicle model
 *               trim:
 *                 type: string
 *                 description: Vehicle trim
 *               status:
 *                 type: string
 *                 description: Application status
 *               documents:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                     filePath:
 *                       type: string
 *             required:
 *               - employeeId
 *               - dniNumber
 *               - dateOfBirth
 *               - incomeSource
 *               - loanAmount
 *               - totalPrice
 *               - tradeInValue
 *               - downPayment
 *               - remainingPrice
 *               - niv
 *               - newPreowned
 *               - year
 *               - make
 *               - model
 *               - trim
 *     responses:
 *       201:
 *         description: Auto loan application created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutoLoan'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      employeeId,
      dniNumber,
      dateOfBirth,
      incomeSource,
      loanAmount,
      totalPrice,
      tradeInValue,
      downPayment,
      remainingPrice,
      niv,
      newPreowned,
      year,
      make,
      model,
      trim,
      status,
      documents
    } = body;

    if (!employeeId || !dniNumber || !dateOfBirth || !incomeSource || 
        !loanAmount || !totalPrice || !tradeInValue || !downPayment || 
        !remainingPrice || !niv || !newPreowned || !year || !make || !model || !trim) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    const autoLoan = await prisma.autoLoan.create({
      data: {
        employeeId,
        dniNumber,
        dateOfBirth: new Date(dateOfBirth),
        incomeSource,
        loanAmount,
        totalPrice,
        tradeInValue,
        downPayment,
        remainingPrice,
        niv,
        newPreowned,
        year,
        make,
        model,
        trim,
        status: status || "submitted",
        documents: documents ? { create: documents } : undefined,
      },
      include: { documents: true, employee: true },
    });

    return NextResponse.json(autoLoan, { status: 201 });
  } catch (err) {
    console.error("Error creating auto loan:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
