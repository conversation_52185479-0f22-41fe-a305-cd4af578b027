import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/auto-loans/{id}/documents:
 *   post:
 *     summary: Add documents to auto loan application
 *     description: Adds new documents to a specific auto loan application
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Auto loan application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                     filePath:
 *                       type: string
 *             required:
 *               - documents
 *     responses:
 *       200:
 *         description: Documents added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutoLoan'
 *       400:
 *         description: Invalid ID or missing documents
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Auto loan application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    const body = await req.json();
    const { documents } = body;

    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      return NextResponse.json({ error: "Documents array is required" }, { status: 400 });
    }

    // Check if auto loan exists
    const autoLoan = await prisma.autoLoan.findUnique({
      where: { id },
    });

    if (!autoLoan) {
      return NextResponse.json({ error: "Auto loan application not found" }, { status: 404 });
    }

    // Add documents to the auto loan
    await prisma.autoLoanDocument.createMany({
      data: documents.map((doc: { fileName: string; filePath: string }) => ({
        autoLoanId: id,
        fileName: doc.fileName,
        filePath: doc.filePath,
      })),
    });

    // Return updated auto loan with documents
    const updatedAutoLoan = await prisma.autoLoan.findUnique({
      where: { id },
      include: {
        employee: true,
        documents: true,
      },
    });

    return NextResponse.json(updatedAutoLoan);
  } catch (err) {
    console.error("Error adding documents to auto loan:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
