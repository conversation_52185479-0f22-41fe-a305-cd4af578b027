import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/auto-loans/{id}:
 *   get:
 *     summary: Get auto loan application by ID
 *     description: Returns a specific auto loan application with employee and document information
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Auto loan application ID
 *     responses:
 *       200:
 *         description: Auto loan application details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutoLoan'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Auto loan application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    const autoLoan = await prisma.autoLoan.findUnique({
      where: { id },
      include: {
        employee: true,
        documents: true,
      },
    });

    if (!autoLoan) return NextResponse.json({ error: "Auto loan application not found" }, { status: 404 });

    return NextResponse.json(autoLoan);
  } catch (err) {
    console.error("Error fetching auto loan:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/auto-loans/{id}:
 *   put:
 *     summary: Update auto loan application by ID
 *     description: Updates a specific auto loan application
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Auto loan application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dniNumber:
 *                 type: string
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *               incomeSource:
 *                 type: string
 *               loanAmount:
 *                 type: number
 *               totalPrice:
 *                 type: number
 *               tradeInValue:
 *                 type: number
 *               downPayment:
 *                 type: number
 *               remainingPrice:
 *                 type: number
 *               niv:
 *                 type: string
 *               newPreowned:
 *                 type: string
 *               year:
 *                 type: integer
 *               make:
 *                 type: string
 *               model:
 *                 type: string
 *               trim:
 *                 type: string
 *               status:
 *                 type: string
 *     responses:
 *       200:
 *         description: Auto loan application updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutoLoan'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Auto loan application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    const body = await req.json();
    const updateData: any = { ...body };

    // Convert dateOfBirth to Date if provided
    if (updateData.dateOfBirth) {
      updateData.dateOfBirth = new Date(updateData.dateOfBirth);
    }

    const autoLoan = await prisma.autoLoan.update({
      where: { id },
      data: updateData,
      include: {
        employee: true,
        documents: true,
      },
    });

    return NextResponse.json(autoLoan);
  } catch (err) {
    console.error("Error updating auto loan:", err);
    if (err instanceof Error && err.message.includes("Record to update not found")) {
      return NextResponse.json({ error: "Auto loan application not found" }, { status: 404 });
    }
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/auto-loans/{id}:
 *   delete:
 *     summary: Delete auto loan application by ID
 *     description: Deletes a specific auto loan application and its associated documents
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Auto loan application ID
 *     responses:
 *       200:
 *         description: Auto loan application deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Auto loan application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // Delete the auto loan application (documents will be deleted due to cascade)
    await prisma.autoLoan.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Auto loan application deleted successfully" });
  } catch (err) {
    console.error("Error deleting auto loan:", err);
    if (err instanceof Error && err.message.includes("Record to delete does not exist")) {
      return NextResponse.json({ error: "Auto loan application not found" }, { status: 404 });
    }
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
