import { NextRequest, NextResponse } from "next/server";
import { searchFaqs } from "@/lib/x-stream-faq";

/**
 * @swagger
 * /api/x-stream-faq:
 *   get:
 *     summary: Search FAQs
 *     description: Search for FAQs using a query string
 *     tags:
 *       - X-Stream FAQ
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         required: true
 *         description: Search query
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by tags (can be specified multiple times)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Search results
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const query = url.searchParams.get("q");
    const category = url.searchParams.get("category") || undefined;
    const tags = url.searchParams.getAll("tags") || undefined;
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);

    // If no query parameter is provided, return a bad request
    if (!query) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      );
    }

    const results = await searchFaqs(query, {
      category,
      tags: tags.length > 0 ? tags : undefined,
      page,
      pageSize,
    });

    return NextResponse.json(results);
  } catch (error) {
    console.error("Error searching FAQs:", error);

    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes("auth")) {
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Failed to search FAQs" },
      { status: 500 }
    );
  }
}
