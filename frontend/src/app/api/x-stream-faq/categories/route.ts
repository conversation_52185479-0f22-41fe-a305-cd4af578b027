import { NextResponse } from "next/server";
import { getFaqCategories } from "@/lib/x-stream-faq";

/**
 * @swagger
 * /api/x-stream-faq/categories:
 *   get:
 *     summary: Get FAQ categories
 *     description: Get all available FAQ categories
 *     tags:
 *       - X-Stream FAQ
 *     responses:
 *       200:
 *         description: List of categories
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET() {
  try {
    const categories = await getFaqCategories();
    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching FAQ categories:", error);
    
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes("auth")) {
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to fetch FAQ categories" },
      { status: 500 }
    );
  }
}
