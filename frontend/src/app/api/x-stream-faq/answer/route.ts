import { NextRequest, NextResponse } from "next/server";
import { answerQuestion } from "@/lib/x-stream-faq";

/**
 * @swagger
 * /api/x-stream-faq/answer:
 *   post:
 *     summary: Answer a question using LLM with RAG
 *     description: Uses the LLM inference API to answer a question with Retrieval Augmented Generation
 *     tags:
 *       - X-Stream FAQ
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 description: The question to answer
 *               kb_id:
 *                 type: string
 *                 description: Optional knowledge base ID
 *     responses:
 *       200:
 *         description: Answer to the question with sources
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { query, kb_id } = body;

    // Validate the request
    if (!query) {
      return NextResponse.json(
        { error: "Query is required" },
        { status: 400 }
      );
    }

    // Call the LLM inference API
    const result = await answerQuestion(query, kb_id);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error answering question:", error);
    
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes("auth")) {
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to answer question" },
      { status: 500 }
    );
  }
}
