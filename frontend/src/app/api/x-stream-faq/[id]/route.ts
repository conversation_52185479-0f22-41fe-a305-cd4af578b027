import { NextRequest, NextResponse } from "next/server";
import { getFaqById } from "@/lib/x-stream-faq";

/**
 * @swagger
 * /api/x-stream-faq/{id}:
 *   get:
 *     summary: Get FAQ by ID
 *     description: Get a specific FAQ by its ID
 *     tags:
 *       - X-Stream FAQ
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: FAQ ID
 *     responses:
 *       200:
 *         description: FAQ item
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: FAQ not found
 *       500:
 *         description: Server error
 */
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: "FAQ ID is required" },
        { status: 400 }
      );
    }

    const faq = await getFaqById(id);
    
    if (!faq) {
      return NextResponse.json(
        { error: "FAQ not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(faq);
  } catch (error) {
    console.error("Error fetching FAQ by ID:", error);
    
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes("auth")) {
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }
    
    // Check if it's a 404 error
    if (error instanceof Error && error.message.includes("404")) {
      return NextResponse.json(
        { error: "FAQ not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to fetch FAQ" },
      { status: 500 }
    );
  }
}
