import { NextResponse } from "next/server";
import { getFaqTags } from "@/lib/x-stream-faq";

/**
 * @swagger
 * /api/x-stream-faq/tags:
 *   get:
 *     summary: Get FAQ tags
 *     description: Get all available FAQ tags
 *     tags:
 *       - X-Stream FAQ
 *     responses:
 *       200:
 *         description: List of tags
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET() {
  try {
    const tags = await getFaqTags();
    return NextResponse.json(tags);
  } catch (error) {
    console.error("Error fetching FAQ tags:", error);
    
    // Check if it's an authentication error
    if (error instanceof Error && error.message.includes("auth")) {
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to fetch FAQ tags" },
      { status: 500 }
    );
  }
}
