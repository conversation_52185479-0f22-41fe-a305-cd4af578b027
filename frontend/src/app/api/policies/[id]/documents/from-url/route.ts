import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs/promises";
import path from "path";
import https from "https";
import http from "http";
import { URL } from "url";

const uploadDir = path.join(process.cwd(), "uploads");

/**
 * @swagger
 * /api/policies/{id}/documents/from-url:
 *   post:
 *     summary: Attach a document to a policy from a URL
 *     description: Downloads a document from a URL and attaches it to a policy
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 description: URL of the document to download
 *               fileName:
 *                 type: string
 *                 description: Optional custom file name (if not provided, will be extracted from URL)
 *             required:
 *               - url
 *     responses:
 *       201:
 *         description: Document attached successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PolicyDocument'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/policies/[id]/documents/from-url - attach a document from a URL to a policy
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // Check if policy exists
    const policy = await prisma.policy.findUnique({
      where: { id },
      select: { id: true }
    });

    if (!policy) return NextResponse.json({ error: "Policy not found" }, { status: 404 });

    const body = await req.json();
    const { url, fileName: customFileName } = body;

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Ensure uploads directory exists
    await fs.mkdir(uploadDir, { recursive: true });

    // Download the file from the URL
    const fileData = await downloadFileFromUrl(url);
    if (!fileData) {
      return NextResponse.json({ error: "Failed to download file from URL" }, { status: 500 });
    }

    // Extract filename from URL if not provided
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/');
    const fileNameFromUrl = pathSegments[pathSegments.length - 1] || 'document';
    const fileName = customFileName || fileNameFromUrl;

    // Generate a unique filename to prevent collisions
    const uniqueFileName = Date.now() + "-" + fileName.replace(/[^a-zA-Z0-9.\-_]/g, "_");
    const filePath = path.join(uploadDir, uniqueFileName);

    // Save the file
    await fs.writeFile(filePath, fileData);

    // Create the document record
    const document = await prisma.policyDocument.create({
      data: {
        policyId: id,
        fileName: fileName,
        filePath
      }
    });

    return NextResponse.json(document, { status: 201 });
  } catch (error) {
    console.error("Error attaching document from URL:", error);
    return NextResponse.json({ error: "Failed to attach document from URL" }, { status: 500 });
  }
}

// Helper function to download a file from a URL
async function downloadFileFromUrl(url: string): Promise<Buffer | null> {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const protocol = urlObj.protocol === 'https:' ? https : http;

      protocol.get(url, (response) => {
        // Check if the response is successful
        if (response.statusCode !== 200) {
          console.error(`Failed to download file: HTTP status ${response.statusCode}`);
          resolve(null);
          return;
        }

        // Collect data chunks
        const chunks: Buffer[] = [];
        response.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
        response.on('end', () => {
          const fileData = Buffer.concat(chunks);
          resolve(fileData);
        });
      }).on('error', (err) => {
        console.error('Error downloading file:', err);
        resolve(null);
      });
    } catch (error) {
      console.error('Error parsing URL or downloading file:', error);
      resolve(null);
    }
  });
}
