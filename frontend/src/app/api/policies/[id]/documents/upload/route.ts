import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import path from "path";
import fs from "fs/promises";

const uploadDir = path.join(process.cwd(), "uploads");

/**
 * @swagger
 * /api/policies/{id}/documents/upload:
 *   post:
 *     summary: Upload and attach a document to a policy
 *     description: Uploads a file and attaches it to a policy in a single operation
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: File to upload
 *               customFileName:
 *                 type: string
 *                 description: Optional custom file name (if not provided, original file name will be used)
 *     responses:
 *       201:
 *         description: Document uploaded and attached successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PolicyDocument'
 *       400:
 *         description: Invalid ID or no file uploaded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/policies/[id]/documents/upload - upload and attach a document to a policy
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // Check if policy exists
    const policy = await prisma.policy.findUnique({
      where: { id },
      select: { id: true }
    });

    if (!policy) return NextResponse.json({ error: "Policy not found" }, { status: 404 });

    // Ensure uploads directory exists
    await fs.mkdir(uploadDir, { recursive: true });

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("file");
    const customFileName = formData.get("customFileName") as string | null;

    if (!file || typeof file === "string") {
      return NextResponse.json({ error: "No file uploaded or invalid file" }, { status: 400 });
    }

    // Process the file
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Use custom file name if provided, otherwise use original name
    const originalFileName = file.name;
    const displayFileName = customFileName || originalFileName;
    
    // Create a unique filename for storage
    const uniqueFileName = Date.now() + "-" + originalFileName.replace(/[^a-zA-Z0-9.\-_]/g, "_");
    const filePath = path.join(uploadDir, uniqueFileName);
    
    // Save the file
    await fs.writeFile(filePath, buffer);

    // Create the document record
    const document = await prisma.policyDocument.create({
      data: {
        policyId: id,
        fileName: displayFileName,
        filePath
      }
    });

    return NextResponse.json(document, { status: 201 });
  } catch (error) {
    console.error("Error uploading policy document:", error);
    return NextResponse.json({ error: "Failed to upload policy document" }, { status: 500 });
  }
}
