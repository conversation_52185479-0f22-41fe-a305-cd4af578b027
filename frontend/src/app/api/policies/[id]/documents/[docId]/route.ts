import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs/promises";
import { existsSync } from "fs";

/**
 * @swagger
 * /api/policies/{id}/documents/{docId}:
 *   get:
 *     summary: Get a policy document by ID
 *     description: Returns a specific document associated with a policy
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Policy document
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PolicyDocument'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/policies/[id]/documents/[docId] - get a policy document by ID
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const policyId = Number(params.id);
    const docId = Number(params.docId);
    
    if (isNaN(policyId) || isNaN(docId)) {
      return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    }

    // Find the document that belongs to the policy
    const document = await prisma.policyDocument.findFirst({
      where: {
        id: docId,
        policyId: policyId,
      },
    });

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 });
    }

    return NextResponse.json(document);
  } catch (error) {
    console.error("Error fetching policy document:", error);
    return NextResponse.json({ error: "Failed to fetch policy document" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/policies/{id}/documents/{docId}:
 *   delete:
 *     summary: Delete a policy document
 *     description: Deletes a document associated with a policy and removes the file from the filesystem
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// DELETE /api/policies/[id]/documents/[docId] - delete a policy document
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const policyId = Number(params.id);
    const docId = Number(params.docId);
    
    if (isNaN(policyId) || isNaN(docId)) {
      return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    }

    // Find the document that belongs to the policy
    const document = await prisma.policyDocument.findFirst({
      where: {
        id: docId,
        policyId: policyId,
      },
    });

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 });
    }

    // Get the file path before deleting the document
    const filePath = document.filePath;

    // Delete the document from the database
    await prisma.policyDocument.delete({
      where: {
        id: docId,
      },
    });

    // Delete the file from the filesystem if it exists
    if (filePath && existsSync(filePath)) {
      try {
        await fs.unlink(filePath);
        console.log(`Deleted file: ${filePath}`);
      } catch (fileError) {
        console.error(`Error deleting file ${filePath}:`, fileError);
        // We don't want to fail the API call if the file deletion fails
        // The document is already deleted from the database
      }
    }

    return NextResponse.json({ message: "Document deleted successfully" });
  } catch (error) {
    console.error("Error deleting policy document:", error);
    return NextResponse.json({ error: "Failed to delete policy document" }, { status: 500 });
  }
}
