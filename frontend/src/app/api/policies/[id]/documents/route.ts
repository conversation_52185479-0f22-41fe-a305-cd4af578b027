import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/policies/{id}/documents:
 *   get:
 *     summary: Get documents for a policy
 *     description: Returns all documents associated with a policy
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     responses:
 *       200:
 *         description: List of policy documents
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/PolicyDocument'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/policies/[id]/documents - get documents for a policy
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // Check if policy exists
    const policy = await prisma.policy.findUnique({
      where: { id },
      select: { id: true }
    });

    if (!policy) return NextResponse.json({ error: "Policy not found" }, { status: 404 });

    // Get documents for the policy
    const documents = await prisma.policyDocument.findMany({
      where: { policyId: id },
      orderBy: { uploadedAt: 'desc' }
    });

    return NextResponse.json(documents);
  } catch (error) {
    console.error("Error fetching policy documents:", error);
    return NextResponse.json({ error: "Failed to fetch policy documents" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/policies/{id}/documents:
 *   post:
 *     summary: Add a document to a policy
 *     description: Uploads a new document and associates it with a policy
 *     tags:
 *       - Policy Documents
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileName:
 *                 type: string
 *                 description: Name of the file
 *               filePath:
 *                 type: string
 *                 description: Path to the file on the server
 *             required:
 *               - fileName
 *               - filePath
 *     responses:
 *       201:
 *         description: Document added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PolicyDocument'
 *       400:
 *         description: Invalid ID or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/policies/[id]/documents - add a document to a policy
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // Check if policy exists
    const policy = await prisma.policy.findUnique({
      where: { id },
      select: { id: true }
    });

    if (!policy) return NextResponse.json({ error: "Policy not found" }, { status: 404 });

    const body = await req.json();
    const { fileName, filePath } = body;

    if (!fileName || !filePath) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Create the document
    const document = await prisma.policyDocument.create({
      data: {
        policyId: id,
        fileName,
        filePath
      }
    });

    return NextResponse.json(document, { status: 201 });
  } catch (error) {
    console.error("Error adding policy document:", error);
    return NextResponse.json({ error: "Failed to add policy document" }, { status: 500 });
  }
}
