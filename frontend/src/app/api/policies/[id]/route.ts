import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs/promises";
import { existsSync } from "fs";

/**
 * @swagger
 * /api/policies/{id}:
 *   get:
 *     summary: Get policy by ID
 *     description: Returns a single policy by its ID
 *     tags:
 *       - Policies
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     responses:
 *       200:
 *         description: Policy found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Policy'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/policies/[id] - get policy by ID
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    const policy = await prisma.policy.findUnique({
      where: { id },
      include: { employee: true, documents: true },
    });
    if (!policy) return NextResponse.json({ error: "Policy not found" }, { status: 404 });
    return NextResponse.json(policy);
  } catch (error) {
    console.error("Error fetching policy:", error);
    return NextResponse.json({ error: "Failed to fetch policy" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/policies/{id}:
 *   put:
 *     summary: Update policy by ID
 *     description: Updates an existing policy
 *     tags:
 *       - Policies
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               policyOwner:
 *                 type: string
 *               insured:
 *                 type: string
 *               spouse:
 *                 type: string
 *               group:
 *                 type: string
 *               policyNumber:
 *                 type: string
 *               originalEffectiveDate:
 *                 type: string
 *                 format: date-time
 *               scheduledEffectiveDate:
 *                 type: string
 *                 format: date-time
 *               issuedAge:
 *                 type: integer
 *               insuredCoverage:
 *                 type: number
 *                 format: float
 *               spouseCoverage:
 *                 type: number
 *                 format: float
 *     responses:
 *       200:
 *         description: Policy updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Policy'
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// PUT /api/policies/[id] - update policy by ID
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    const body = await req.json();
    const policy = await prisma.policy.update({
      where: { id },
      data: body,
      include: { employee: true, documents: true },
    });
    return NextResponse.json(policy);
  } catch (error) {
    console.error("Error updating policy:", error);
    return NextResponse.json({ error: "Failed to update policy" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/policies/{id}:
 *   delete:
 *     summary: Delete policy by ID
 *     description: Deletes a policy and all its associated documents
 *     tags:
 *       - Policies
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Policy ID
 *     responses:
 *       200:
 *         description: Policy deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Policy not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// DELETE /api/policies/[id] - delete policy by ID
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // First, check if the policy exists
    const policy = await prisma.policy.findUnique({
      where: { id },
      include: { documents: true },
    });

    if (!policy) {
      return NextResponse.json({ error: "Policy not found" }, { status: 404 });
    }

    // Delete all associated documents and their files
    if (policy.documents.length > 0) {
      // Delete files from filesystem first
      for (const document of policy.documents) {
        const filePath = document.filePath;
        if (filePath && existsSync(filePath)) {
          try {
            await fs.unlink(filePath);
            console.log(`Deleted file: ${filePath}`);
          } catch (fileError) {
            console.error(`Error deleting file ${filePath}:`, fileError);
            // Continue with deletion even if file removal fails
          }
        }
      }

      // Then delete document records from database
      await prisma.policyDocument.deleteMany({
        where: { policyId: id },
      });
    }

    // Delete the policy
    await prisma.policy.delete({ where: { id } });

    return NextResponse.json({ message: "Policy deleted successfully" });
  } catch (error) {
    console.error("Error deleting policy:", error);
    return NextResponse.json({ error: "Failed to delete policy" }, { status: 500 });
  }
}
