import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/policies:
 *   get:
 *     summary: Get all policies
 *     description: Returns a list of all policies with their employee and document information.
 *     tags:
 *       - Policies
 *     responses:
 *       200:
 *         description: List of policies
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Policy'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/policies - list all policies
export async function GET(/* _req: NextRequest */) {
  try {
    const policies = await prisma.policy.findMany({
      include: { employee: true, documents: true },
    });
    return NextResponse.json(policies);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/policies:
 *   post:
 *     summary: Create a new policy
 *     description: Creates a new policy for an employee
 *     tags:
 *       - Policies
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employeeId:
 *                 type: integer
 *                 description: ID of the employee
 *               policyOwner:
 *                 type: string
 *                 description: Name of the policy owner
 *               insured:
 *                 type: string
 *                 description: Name of the insured person
 *               spouse:
 *                 type: string
 *                 description: Name of the spouse (if applicable)
 *               group:
 *                 type: string
 *                 description: Group identifier
 *               policyNumber:
 *                 type: string
 *                 description: Policy number
 *               originalEffectiveDate:
 *                 type: string
 *                 format: date-time
 *                 description: Original effective date of the policy
 *               scheduledEffectiveDate:
 *                 type: string
 *                 format: date-time
 *                 description: Scheduled effective date of the policy
 *               issuedAge:
 *                 type: integer
 *                 description: Age at which the policy was issued
 *               insuredCoverage:
 *                 type: number
 *                 format: float
 *                 description: Coverage amount for the insured
 *               spouseCoverage:
 *                 type: number
 *                 format: float
 *                 description: Coverage amount for the spouse
 *               documents:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                     filePath:
 *                       type: string
 *             required:
 *               - employeeId
 *               - policyOwner
 *               - insured
 *               - group
 *               - policyNumber
 *     responses:
 *       201:
 *         description: Policy created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Policy'
 *       400:
 *         description: Bad request - missing required fields or employee already has a policy
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Employee not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/policies - create a new policy
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      employeeId,
      policyOwner,
      insured,
      spouse,
      group,
      policyNumber,
      originalEffectiveDate,
      scheduledEffectiveDate,
      issuedAge,
      insuredCoverage,
      spouseCoverage,
      documents
    } = body;

    if (!employeeId || !policyOwner || !insured || !group || !policyNumber) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if employee exists
    const employee = await prisma.employee.findUnique({
      where: { id: employeeId },
      include: { policy: true }
    });

    if (!employee) {
      return NextResponse.json({ error: "Employee not found" }, { status: 404 });
    }

    // Check if employee already has a policy
    if (employee.policy) {
      return NextResponse.json({ error: "Employee already has a policy" }, { status: 400 });
    }

    const policy = await prisma.policy.create({
      data: {
        employeeId,
        policyOwner,
        insured,
        spouse,
        group,
        policyNumber,
        originalEffectiveDate: originalEffectiveDate ? new Date(originalEffectiveDate) : null,
        scheduledEffectiveDate: scheduledEffectiveDate ? new Date(scheduledEffectiveDate) : null,
        issuedAge,
        insuredCoverage,
        spouseCoverage,
        documents: documents ? { create: documents } : undefined,
      },
      include: { documents: true, employee: true },
    });

    return NextResponse.json(policy, { status: 201 });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
