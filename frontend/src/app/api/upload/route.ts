import { NextRequest, NextResponse } from "next/server";
import path from "path";
import fs from "fs/promises";

const uploadDir = path.join(process.cwd(), "uploads");

/**
 * @swagger
 * /api/upload:
 *   post:
 *     summary: Upload files
 *     description: Uploads one or more files to the server
 *     tags:
 *       - File Upload
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Files to upload
 *     responses:
 *       200:
 *         description: Files uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 files:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       originalname:
 *                         type: string
 *                       filename:
 *                         type: string
 *                       path:
 *                         type: string
 *                       size:
 *                         type: integer
 *                       mimetype:
 *                         type: string
 *       400:
 *         description: No files uploaded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

export async function POST(req: NextRequest) {
  try {
    // Ensure uploads directory exists
    await fs.mkdir(uploadDir, { recursive: true });
    const formData = await req.formData();
    const files = formData.getAll("files");
    if (!files || files.length === 0) {
      return NextResponse.json({ error: "No files uploaded" }, { status: 400 });
    }
    const savedFiles = [];
    for (const file of files) {
      if (typeof file === "string") continue;
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const fileName = Date.now() + "-" + file.name.replace(/[^a-zA-Z0-9.\-_]/g, "_");
      const filePath = path.join(uploadDir, fileName);
      await fs.writeFile(filePath, buffer);
      savedFiles.push({
        originalname: file.name,
        filename: fileName,
        path: filePath,
        size: buffer.length,
        mimetype: file.type,
      });
    }
    return NextResponse.json({ files: savedFiles });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Upload failed';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/upload:
 *   get:
 *     summary: Upload endpoint info
 *     description: Returns information about the upload endpoint
 *     tags:
 *       - File Upload
 *     responses:
 *       200:
 *         description: Information message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
export async function GET() {
  return NextResponse.json({ message: "Use POST to upload files." });
}