import { NextRequest, NextResponse } from "next/server";

/**
 * @swagger
 * /api/claim-status/{id}:
 *   get:
 *     summary: Get claim status from external API
 *     description: Fetches claim status from the external API
 *     tags:
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID
 *     responses:
 *       200:
 *         description: Claim status data
 *       500:
 *         description: Server error
 */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    // console.log(context.params);
    console.log(`Fetching claim status for ID: ${id}`);

    // Construct the URL with the claim ID
    const url = `https://n8n-uniphore.fschaefer-next-cloud.synology.me/webhook/4e5c57a3-ced1-45b4-a885-cda774b70db7?claimId=${id}`;

    // Set up headers with the API key
    const headers = {
      "api-key": "NkrZPco7C1tVn6",
    };

    // Make the request to the external API
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(
        `External API returned ${response.status}: ${response.statusText}`,
      );
    }

    const data = await response.json();

    // Return the data from the external API
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching claim status:", error);
    return NextResponse.json(
      { error: "Failed to fetch claim status" },
      { status: 500 },
    );
  }
}
