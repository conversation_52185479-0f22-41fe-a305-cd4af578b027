import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/employees:
 *   get:
 *     summary: Get all employees
 *     description: Returns a list of all employees
 *     tags:
 *       - Employees
 *     responses:
 *       200:
 *         description: List of employees
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Employee'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// GET /api/employees - list all employees
export async function GET(/* _req: NextRequest */) {
  try {
    const employees = await prisma.employee.findMany();
    return NextResponse.json(employees);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/employees:
 *   post:
 *     summary: Create a new employee
 *     description: Creates a new employee record
 *     tags:
 *       - Employees
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: First name of the employee
 *               lastName:
 *                 type: string
 *                 description: Last name of the employee
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the employee
 *               phone:
 *                 type: string
 *                 description: Phone number of the employee
 *               address:
 *                 type: string
 *                 description: Address of the employee
 *               employerName:
 *                 type: string
 *                 description: Name of the employer
 *               groupId:
 *                 type: string
 *                 description: Group ID of the employee
 *               memberId:
 *                 type: string
 *                 description: Member ID of the employee
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *     responses:
 *       201:
 *         description: Employee created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Employee'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// POST /api/employees - create a new employee
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      employerName,
      groupId,
      memberId
    } = body;

    if (!firstName || !lastName || !email) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    const employee = await prisma.employee.create({
      data: {
        firstName,
        lastName,
        email,
        phone,
        address,
        employerName,
        groupId,
        memberId
      }
    });

    return NextResponse.json(employee, { status: 201 });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}