import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs/promises";
import { existsSync } from "fs";

/**
 * @swagger
 * /api/claims/{id}:
 *   get:
 *     summary: Get a claim by ID
 *     description: Retrieve a claim by its ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *     responses:
 *       200:
 *         description: Claim details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Claim'
 *       404:
 *         description: Claim not found
 */

// GET /api/claims/[id] - get claim by ID
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    const claim = await prisma.claim.findUnique({
      where: { id },
      include: {
        employee: {
          include: {
            policy: {
              include: {
                documents: true
              }
            }
          }
        },
        documents: true,
        comments: true
      },
    });
    if (!claim) return NextResponse.json({ error: "Claim not found" }, { status: 404 });
    return NextResponse.json(claim);
  } catch (error) {
    console.error("Error fetching claim:", error);
    return NextResponse.json({ error: "Failed to fetch claim" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/claims/{id}:
 *   put:
 *     summary: Update a claim
 *     description: Update an existing claim by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ClaimUpdate'
 *     responses:
 *       200:
 *         description: Updated claim
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Claim'
 *       404:
 *         description: Claim not found
 */

// PUT /api/claims/[id] - update claim by ID
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    const body = await req.json();
    const claim = await prisma.claim.update({
      where: { id },
      data: body,
      include: { employee: true, documents: true, comments: true },
    });
    return NextResponse.json(claim);
  } catch (error) {
    console.error("Error updating claim:", error);
    return NextResponse.json({ error: "Failed to update claim" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/claims/{id}:
 *   delete:
 *     summary: Delete a claim
 *     description: Delete a claim by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *     responses:
 *       200:
 *         description: Claim deleted successfully
 *       404:
 *         description: Claim not found
 */

// DELETE /api/claims/[id] - delete claim by ID
export async function DELETE(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });

    // First, check if the claim exists
    const claim = await prisma.claim.findUnique({
      where: { id },
      include: { documents: true, comments: true },
    });

    if (!claim) {
      return NextResponse.json({ error: "Claim not found" }, { status: 404 });
    }

    // Delete all associated documents and their files
    if (claim.documents.length > 0) {
      // Delete files from filesystem first
      for (const document of claim.documents) {
        const filePath = document.filePath;
        if (filePath && existsSync(filePath)) {
          try {
            await fs.unlink(filePath);
            console.log(`Deleted file: ${filePath}`);
          } catch (fileError) {
            console.error(`Error deleting file ${filePath}:`, fileError);
            // Continue with deletion even if file removal fails
          }
        }
      }

      // Then delete document records from database
      await prisma.claimDocument.deleteMany({
        where: { claimId: id },
      });
    }

    // Delete all associated comments
    if (claim.comments.length > 0) {
      await prisma.claimComment.deleteMany({
        where: { claimId: id },
      });
    }

    // Now delete the claim
    await prisma.claim.delete({ where: { id } });

    return NextResponse.json({ message: "Claim deleted successfully" });
  } catch (error) {
    console.error("Error deleting claim:", error);
    return NextResponse.json({ error: "Failed to delete claim" }, { status: 500 });
  }
}
