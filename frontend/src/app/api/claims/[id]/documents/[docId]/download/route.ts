import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs";
import path from "path";

/**
 * @swagger
 * /api/claims/{id}/documents/{docId}/download:
 *   get:
 *     summary: Download a document
 *     description: Download a document file associated with a claim
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Document not found
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const claimId = Number(params.id);
    const docId = Number(params.docId);
    if (isNaN(claimId) || isNaN(docId)) {
      return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    }

    // Check if this is a view request or a download request
    const { searchParams } = new URL(req.url);
    const isViewRequest = searchParams.get('view') === 'true';

    console.log('View request:', isViewRequest); // Debug log

    const document = await prisma.claimDocument.findFirst({
      where: {
        id: docId,
        claim: {
          id: claimId,
        },
      },
    });

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 });
    }

    const filePath = document.filePath;
    if (!filePath || !fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: "Document file not found" },
        { status: 404 }
      );
    }

    const fileBuffer = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);

    // Determine content type based on file extension
    const fileExtension = path.extname(fileName).toLowerCase();
    let contentType = "application/octet-stream";

    if (fileExtension === '.pdf') {
      contentType = "application/pdf";
    } else if (['.jpg', '.jpeg'].includes(fileExtension)) {
      contentType = "image/jpeg";
    } else if (fileExtension === '.png') {
      contentType = "image/png";
    } else if (fileExtension === '.gif') {
      contentType = "image/gif";
    } else if (fileExtension === '.bmp') {
      contentType = "image/bmp";
    } else if (fileExtension === '.webp') {
      contentType = "image/webp";
    }

    console.log('Content type:', contentType); // Debug log
    console.log('File extension:', fileExtension); // Debug log

    // Set appropriate Content-Disposition header based on request type
    const contentDisposition = isViewRequest
      ? `inline; filename="${fileName}"`
      : `attachment; filename="${fileName}"`;

    console.log('Content-Disposition:', contentDisposition); // Debug log

    // Create headers object
    const headers = new Headers({
      "Content-Disposition": contentDisposition,
      "Content-Type": contentType,
    });

    // For PDFs being viewed, add additional headers to prevent download
    if (isViewRequest && fileExtension === '.pdf') {
      headers.set("X-Content-Type-Options", "nosniff");
      headers.set("Cache-Control", "public, max-age=3600");
    }

    return new Response(fileBuffer, { headers });
  } catch (error) {
    console.error("Error downloading document:", error);
    return NextResponse.json(
      { error: "Failed to download document" },
      { status: 500 }
    );
  }
}
