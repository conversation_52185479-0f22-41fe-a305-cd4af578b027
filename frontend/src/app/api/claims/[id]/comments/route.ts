import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/claims/{id}/comments:
 *   get:
 *     summary: Get comments for a claim
 *     description: Retrieve all comments for a specific claim
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *     responses:
 *       200:
 *         description: List of comments
 *       404:
 *         description: Claim not found
 */

// GET /api/claims/[id]/comments - get comments for a claim
export async function GET(
  _req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    
    // Check if claim exists
    const claim = await prisma.claim.findUnique({
      where: { id },
      select: { id: true }
    });
    
    if (!claim) return NextResponse.json({ error: "Claim not found" }, { status: 404 });
    
    // Get comments for the claim
    const comments = await prisma.claimComment.findMany({
      where: { claimId: id },
      orderBy: { createdAt: 'desc' }
    });
    
    return NextResponse.json(comments);
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json({ error: "Failed to fetch comments" }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/claims/{id}/comments:
 *   post:
 *     summary: Add a comment to a claim
 *     description: Add a new comment to a specific claim
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: The comment text
 *     responses:
 *       201:
 *         description: Comment added successfully
 *       404:
 *         description: Claim not found
 */

// POST /api/claims/[id]/comments - add a comment to a claim
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const id = Number(params.id);
    if (isNaN(id)) return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    
    // Check if claim exists
    const claim = await prisma.claim.findUnique({
      where: { id },
      select: { id: true }
    });
    
    if (!claim) return NextResponse.json({ error: "Claim not found" }, { status: 404 });
    
    // Get comment text from request body
    const body = await req.json();
    const { text } = body;
    
    if (!text || typeof text !== 'string') {
      return NextResponse.json({ error: "Comment text is required" }, { status: 400 });
    }
    
    // Create the comment
    const comment = await prisma.claimComment.create({
      data: {
        claimId: id,
        text
      }
    });
    
    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error("Error adding comment:", error);
    return NextResponse.json({ error: "Failed to add comment" }, { status: 500 });
  }
}
