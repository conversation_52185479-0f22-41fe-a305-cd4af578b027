import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/claims:
 *   get:
 *     summary: Get all claims
 *     description: Returns a list of all claims with their documents and employee info.
 *     responses:
 *       200:
 *         description: List of claims
 *   post:
 *     summary: Create a new claim
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: Claim created
 * @swagger
 * /api/claims/{id}/documents/{docId}/download:
 *   get:
 *     summary: Download a document file for a claim
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Claim ID
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Document ID
 *     responses:
 *       200:
 *         description: File downloaded successfully
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Document not found
 */
// GET /api/claims - list all claims
export async function GET(/* _req: NextRequest */) {
  try {
    const claims = await prisma.claim.findMany({
      include: {
        documents: true,
        employee: {
          include: {
            policy: true
          }
        }
      },
    });
    return NextResponse.json(claims);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// POST /api/claims - create a new claim
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      employeeId,
      claimType,
      description,
      incidentDate,
      status,
      documents
    } = body;

    if (!employeeId || !description) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    const claim = await prisma.claim.create({
      data: {
        employeeId,
        claimType: claimType || "Critical Illness",
        description,
        incidentDate: incidentDate ? new Date(incidentDate) : null,
        status: status || "submitted",
        documents: documents ? { create: documents } : undefined,
      },
      include: { documents: true, employee: true },
    });
    return NextResponse.json(claim, { status: 201 });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

