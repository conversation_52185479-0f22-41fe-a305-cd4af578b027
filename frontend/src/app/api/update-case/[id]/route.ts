import { NextRequest, NextResponse } from "next/server";

/**
 * @swagger
 * /api/update-case/{id}:
 *   get:
 *     summary: Get case update summary
 *     description: Fetches a summary of the current case status
 *     tags:
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID
 *     responses:
 *       200:
 *         description: Case update summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary:
 *                   type: string
 *       500:
 *         description: Server error
 *   post:
 *     summary: Submit edited case update
 *     description: Submits the edited case update text
 *     tags:
 *       - Claims
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Claim ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               summary:
 *                 type: string
 *     responses:
 *       200:
 *         description: Case update submitted successfully
 *       500:
 *         description: Server error
 */

// GET /api/update-case/[id] - get case update summary
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    console.log(`Fetching case update for claim ID: ${id}`);

    // Construct the URL with the claim ID
    const url = `https://n8n-uniphore.fschaefer-next-cloud.synology.me/webhook/a50e98b0-2bc5-4a70-a330-3f247efb807b?claimId=${id}`;

    // Set up headers with the API key
    const headers = {
      "api-key": "NkrZPco7C1tVn6",
    };

    // Make the request to the external API
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(
        `External API returned ${response.status}: ${response.statusText}`,
      );
    }

    // Parse the response data
    const data = await response.json();

    // The API returns an array with a single item
    if (!data.summary) {
      throw new Error("Invalid response format from external API");
    }

    // Extract the summary from the response
    const summary = data.summary;

    // Return the summary
    return NextResponse.json({ summary });
  } catch (error) {
    console.error("Error fetching case update:", error);
    return NextResponse.json(
      { error: "Failed to fetch case update" },
      { status: 500 },
    );
  }
}

// POST /api/update-case/[id] - submit edited case update
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const body = await req.json();
    const { summary } = body;

    console.log(`Submitting case update for claim ID: ${id}`);
    console.log(`Summary: ${summary}`);

    // TODO: In the future, this will call an external API
    // For now, just return success

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error submitting case update:", error);
    return NextResponse.json(
      { error: "Failed to submit case update" },
      { status: 500 },
    );
  }
}
