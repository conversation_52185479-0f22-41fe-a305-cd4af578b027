/* Examiner theme colors - Next Best Action Edition */
.examiner-theme {
  /* Sidebar colors */
  --sidebar-bg: #2e2e2e;
  --sidebar-darker: #1a1a1a;
  --sidebar-lighter: #3d3d3d;
  --sidebar-text: #ffffff;
  --sidebar-accent: #ff7a45;
  --sidebar-hover: rgba(255, 255, 255, 0.1);
  --sidebar-active: #ff7a45;
  --sidebar-panel-bg: #ffffff;
  --sidebar-panel-darker: #f5f5f5;
  --sidebar-panel-lighter: #ffffff;
  --sidebar-panel-border: rgba(0, 0, 0, 0.1);
  --sidebar-panel-shadow: rgba(0, 0, 0, 0.1);
  --sidebar-panel-text: #333333;
  --sidebar-panel-heading: #333333;
  --sidebar-panel-subheading: #666666;
}

/* Examiner sidebar styling */
.examiner-theme .sidebar {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  border-left: none;
  box-shadow: none;
}

.examiner-theme .sidebar-icon {
  color: var(--sidebar-text);
  transition: all 0.2s ease;
  border-radius: 0;
}

.examiner-theme .sidebar-icon:hover {
  background-color: var(--sidebar-hover);
  transform: none;
  box-shadow: none;
}

.examiner-theme .sidebar-icon.active {
  background-color: var(--sidebar-active);
  color: white;
  box-shadow: none;
  border-radius: 0;
}

.examiner-theme .sidebar-panel {
  background-color: var(--sidebar-panel-bg);
  color: var(--sidebar-panel-text);
  border-left: none;
  box-shadow: -2px 0 10px var(--sidebar-panel-shadow);
}

.examiner-theme .sidebar-panel h2 {
  color: var(--sidebar-panel-heading);
  border-bottom: 1px solid var(--sidebar-panel-border);
  padding-bottom: 0.75rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  letter-spacing: 0;
  font-size: 1.25rem;
}

/* Panel content styling */
.examiner-theme .panel-content {
  color: var(--sidebar-panel-text);
}

.examiner-theme .panel-section {
  background-color: var(--sidebar-panel-lighter);
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: none;
  border: 1px solid var(--sidebar-panel-border);
}

.examiner-theme .panel-section h3 {
  color: var(--sidebar-panel-heading);
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.examiner-theme .panel-section p {
  color: var(--sidebar-panel-text);
  opacity: 0.9;
  line-height: 1.5;
}

/* Scrollbar styling */
.examiner-theme .sidebar-panel::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.examiner-theme .sidebar-panel::-webkit-scrollbar-track {
  background: var(--sidebar-panel-darker);
}

.examiner-theme .sidebar-panel::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 4px;
}

.examiner-theme .sidebar-panel::-webkit-scrollbar-thumb:hover {
  background: #aaaaaa;
}

/* Button styling within panels */
.examiner-theme .sidebar-panel button {
  background-color: var(--sidebar-active);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.examiner-theme .sidebar-panel button:hover {
  background-color: #e86a34;
  transform: none;
}

.examiner-theme .sidebar-panel button:active {
  transform: none;
  opacity: 0.9;
}

/* Input styling within panels */
.examiner-theme .sidebar-panel input,
.examiner-theme .sidebar-panel textarea {
  background-color: white;
  border: 1px solid #e0e0e0;
  color: var(--sidebar-panel-text);
  border-radius: 4px;
  padding: 0.5rem;
  width: 100%;
  transition: all 0.2s ease;
}

.examiner-theme .sidebar-panel input:focus,
.examiner-theme .sidebar-panel textarea:focus {
  background-color: white;
  border-color: var(--sidebar-active);
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 122, 69, 0.2);
}

/* List styling within panels */
.examiner-theme .sidebar-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.examiner-theme .sidebar-panel li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.examiner-theme .sidebar-panel li:last-child {
  border-bottom: none;
}

/* Badge styling */
.examiner-theme .sidebar-badge {
  background-color: var(--sidebar-accent);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced examiner panel styling */
.examiner-theme .panel-section h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.examiner-theme .panel-section p {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

/* Action items styling */
.examiner-theme .panel-section button.p-1 {
  cursor: pointer;
  transition: all 0.2s ease;
}

.examiner-theme .panel-section button.p-1:hover {
  transform: scale(1.1);
}

/* Decision support styling */
.examiner-theme .panel-section .grid {
  margin-top: 0.5rem;
}

/* Summary section styling */
.examiner-theme .panel-section textarea {
  resize: none;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.examiner-theme .panel-section textarea:focus {
  border-color: var(--sidebar-accent);
  outline: none;
}

/* Resize handle styling - simplified */
.examiner-theme .resize-handle {
  transition: background-color 0.2s ease;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.examiner-theme .resize-handle:hover {
  background-color: var(--sidebar-accent) !important;
  opacity: 0.3;
}

.examiner-theme .resize-handle:active {
  background-color: var(--sidebar-accent) !important;
  opacity: 0.5;
}

/* Add a visual indicator when resizing */
.examiner-theme .sidebar-panel.resizing {
  user-select: none; /* Prevent text selection during resize */
}

/* Global resizing state */
body.resizing {
  cursor: ew-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: ew-resize !important;
}

/* Ensure content doesn't overlap with resize handle */
.examiner-theme .sidebar-panel > div {
  padding-left: 8px; /* Add a bit of extra padding on the left side */
}
