"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  employerName?: string;
  groupId?: string;
  memberId?: string;
}

interface ClaimDocument {
  id: number;
  claimId: number;
  fileName: string;
  filePath: string;
  uploadedAt: string;
}

interface Claim {
  id: number;
  employeeId: number;
  employee: Employee;
  claimType: string;
  description: string;
  incidentDate: string | null;
  dateFiled: string;
  status: string;
  documents: ClaimDocument[];
}

export default function ClaimsManagementPage() {
  const router = useRouter();
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortField, setSortField] = useState<keyof Claim>("dateFiled");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  useEffect(() => {
    const fetchClaims = async () => {
      try {
        const response = await fetch("/api/claims");
        if (!response.ok) {
          throw new Error("Failed to fetch claims");
        }
        const data = await response.json();
        setClaims(data);
        setFilteredClaims(data);
      } catch (error) {
        console.error("Error fetching claims:", error);
        toast.error("Failed to load claims");
      } finally {
        setLoading(false);
      }
    };

    fetchClaims();
  }, []);

  useEffect(() => {
    // Apply filters and sorting
    let result = [...claims];

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(claim =>
        claim.id.toString().includes(query) ||
        claim.description.toLowerCase().includes(query) ||
        claim.claimType.toLowerCase().includes(query) ||
        `${claim.employee.firstName} ${claim.employee.lastName}`.toLowerCase().includes(query) ||
        claim.employee.email.toLowerCase().includes(query) ||
        claim.status.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      // Handle nested employee properties
      if (sortField === "employee") {
        aValue = `${a.employee.lastName}, ${a.employee.firstName}`;
        bValue = `${b.employee.lastName}, ${b.employee.firstName}`;
      }

      // Handle date comparison
      if (sortField === "dateFiled" || sortField === "incidentDate") {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    setFilteredClaims(result);
  }, [claims, searchQuery, sortField, sortDirection]);

  const handleSort = (field: keyof Claim) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: keyof Claim) => {
    if (field !== sortField) return null;
    return sortDirection === "asc" ? "↑" : "↓";
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusClass = (status: string) => {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case "submitted":
        return "bg-blue-100 text-blue-800 status-submitted";
      case "in review":
        return "bg-yellow-100 text-yellow-800 status-in-review";
      case "approved":
        return "bg-green-100 text-green-800 status-approved";
      case "rejected":
        return "bg-red-100 text-red-800 status-rejected";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return <div className="flex justify-center p-8">Loading claims data...</div>;
  }

  return (
    <div className="space-y-6">
      <CardHeader className="px-0">
        <CardTitle>Claims Management</CardTitle>
      </CardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search claims..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      <div className="overflow-x-auto rounded-lg border">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("id")}
              >
                Claim ID {getSortIcon("id")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("employee")}
              >
                Employee {getSortIcon("employee")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("claimType")}
              >
                Claim Type {getSortIcon("claimType")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("dateFiled")}
              >
                Date Filed {getSortIcon("dateFiled")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                Status {getSortIcon("status")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Documents
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredClaims.length > 0 ? (
              filteredClaims.map((claim) => (
                <tr
                  key={claim.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    router.push(`/claims-management/${claim.id}`);
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {claim.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{`${claim.employee.firstName} ${claim.employee.lastName}`}</div>
                    <div className="text-xs text-gray-400">{claim.employee.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {claim.claimType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(claim.dateFiled)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(claim.status)}`}>
                      {claim.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {claim.documents.length}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <Link href={`/claims-management/${claim.id}`} onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mr-2"
                      >
                        View Details
                      </Button>
                    </Link>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                  No claims found matching your filters
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
