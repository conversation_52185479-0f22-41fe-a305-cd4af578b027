/* Submit Claim theme colors - Customer-Friendly Pink Edition */
.submit-claim-theme {
  /* Color palette - Warm, friendly, pink colors */
  --primary-color: #ec4899; /* Pink-500 */
  --accent-color: #8b5cf6; /* Violet-500 as accent */
  --background-color: #fdf2f8; /* Pink-50 background */
  --font-color: #1e293b; /* Dark slate blue text - easier on eyes */

  /* Derived shades for UI elements */
  --primary-light: #f472b6; /* Pink-400 */
  --primary-dark: #db2777; /* Pink-600 */
  --primary-darker: #be185d; /* Pink-700 */
  --primary-lightest: #fbcfe8; /* Pink-200 */

  --accent-light: #a78bfa; /* Violet-400 */
  --accent-dark: #7c3aed; /* Violet-600 */

  --background-darker: #fce7f3; /* Pink-100 */
  --background-darkest: #f9a8d4; /* Pink-300 */
  --background-lighter: #fdf2f8; /* Pink-50 */

  --font-light: #334155; /* Lighter text */
  --font-lighter: #64748b; /* Even lighter text */
  --font-lightest: #94a3b8; /* Lightest text for secondary info */

  /* System colors */
  --background: 327 73% 97%; /* Pink-50 in HSL */
  --foreground: 215 30% 18%; /* Dark slate blue in HSL */

  /* Card styling */
  --card: 0 0% 100%; /* White in HSL */
  --card-foreground: 215 30% 18%; /* Dark slate blue in HSL */

  /* Popover styling */
  --popover: 0 0% 100%; /* White in HSL */
  --popover-foreground: 215 30% 18%; /* Dark slate blue in HSL */

  /* Primary action styling */
  --primary: 330 81% 60%; /* Pink-500 in HSL */
  --primary-foreground: 0 0% 100%; /* White in HSL */

  /* Secondary styling */
  --secondary: 327 73% 97%; /* Pink-50 in HSL */
  --secondary-foreground: 215 30% 18%; /* Dark slate blue in HSL */

  /* Muted styling */
  --muted: 327 73% 97%; /* Pink-50 in HSL */
  --muted-foreground: 215 25% 27%; /* Muted blue in HSL */

  /* Accent styling */
  --accent: 263 70% 50%; /* Violet-500 in HSL */
  --accent-foreground: 0 0% 100%; /* White in HSL */

  /* Destructive styling */
  --destructive: 0 84% 60%; /* Softer red in HSL */
  --destructive-foreground: 0 0% 100%; /* White in HSL */

  /* Borders and input styling */
  --border: 327 73% 94%; /* Pink-100 in HSL */
  --input: 327 73% 94%; /* Pink-100 in HSL */
  --ring: 330 81% 60%; /* Pink-500 in HSL */
}

/* Submit Claim theme specific styles - Customer-Friendly Pink Edition */
.submit-claim-theme.card {
  border: 1px solid #fce7f3; /* Pink-100 border */
  background-color: white;
  color: var(--font-color);
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-radius: 12px; /* Rounded corners for friendly feel */
}

.submit-claim-theme.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0; /* No top border initially */
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); /* Gradient for visual interest */
  opacity: 0;
  transition: all 0.3s ease;
}

.submit-claim-theme.card:hover {
  box-shadow: 0 8px 24px rgba(236, 72, 153, 0.12);
  transform: translateY(-2px);
}

.submit-claim-theme.card:hover::before {
  height: 4px;
  opacity: 1;
}

/* Card content styling - More spacious and comfortable */
.submit-claim-theme [data-slot="card-content"] {
  position: relative;
  z-index: 1;
  padding: 2rem;
}

.submit-claim-theme [data-slot="card-header"] {
  position: relative;
  z-index: 1;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #fce7f3;
  background-color: #fdf2f8; /* Pink-50 background */
  border-radius: 12px 12px 0 0;
}

.submit-claim-theme [data-slot="card-footer"] {
  position: relative;
  z-index: 1;
  border-top: 1px solid #fce7f3;
  margin-top: 1.5rem;
  padding: 1.5rem 2rem;
  background-color: #fdf2f8;
  border-radius: 0 0 12px 12px;
}

/* Friendly, approachable buttons */
.submit-claim-theme button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(236, 72, 153, 0.25);
  letter-spacing: 0.01em;
}

.submit-claim-theme button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

.submit-claim-theme button:active {
  transform: translateY(0);
  background-color: var(--primary-darker);
}

/* Outline button - Softer, more approachable */
.submit-claim-theme button[variant="outline"] {
  background-color: transparent;
  color: var(--primary-color);
  border: 1.5px solid var(--primary-color);
  box-shadow: none;
}

.submit-claim-theme button[variant="outline"]:hover {
  background-color: rgba(236, 72, 153, 0.05);
  color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Form inputs - Larger, more comfortable */
.submit-claim-theme input,
.submit-claim-theme textarea {
  background-color: white;
  color: var(--font-color);
  border: 1.5px solid #fce7f3;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.submit-claim-theme input:focus,
.submit-claim-theme textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.15);
  outline: none;
}

.submit-claim-theme input::placeholder,
.submit-claim-theme textarea::placeholder {
  color: #94a3b8; /* Softer placeholder color */
  font-size: 0.95rem;
}

/* Friendly, welcoming headings */
.submit-claim-theme .card-title {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 1.4;
  position: relative;
  padding-bottom: 0.75rem;
  margin-bottom: 0.75rem;
  letter-spacing: -0.01em;
}

.submit-claim-theme .card-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.submit-claim-theme .card:hover .card-title::after {
  width: 80px;
}

/* Form labels - Clearer, more helpful */
.submit-claim-theme label {
  color: var(--font-color);
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Form validation - Gentler error messages */
.submit-claim-theme .form-error {
  color: #ef4444; /* Softer red */
  font-size: 0.875rem;
  margin-top: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.submit-claim-theme .form-error::before {
  content: "⚠️";
  font-size: 0.875rem;
}

/* Dropzone styling - More inviting */
.submit-claim-theme .dropzone {
  border: 2px dashed #f9a8d4;
  border-radius: 12px;
  background-color: #fdf2f8;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.submit-claim-theme .dropzone:hover,
.submit-claim-theme .dropzone.active {
  border-color: var(--primary-color);
  background-color: rgba(236, 72, 153, 0.05);
}

/* Progress bar - More visible and friendly */
.submit-claim-theme .progress-bar {
  height: 8px;
  background-color: #fce7f3;
  border-radius: 4px;
  overflow: hidden;
  margin: 1.25rem 0;
}

.submit-claim-theme .progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 4px;
  transition: width 0.4s ease;
}

/* Success and error messages - More noticeable but friendly */
.submit-claim-theme .success-message {
  color: #db2777; /* Pink-600 */
  background-color: #fdf2f8;
  border: 1px solid #f9a8d4;
  border-radius: 8px;
  padding: 1rem 1.25rem;
  margin: 1.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.submit-claim-theme .success-message::before {
  content: "✅";
  font-size: 1.125rem;
}

.submit-claim-theme .error-message {
  color: #b91c1c; /* Softer red */
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 1rem 1.25rem;
  margin: 1.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.submit-claim-theme .error-message::before {
  content: "⚠️";
  font-size: 1.125rem;
}

/* Confirmation card styling - Celebratory and reassuring */
.submit-claim-theme .confirmation-card {
  text-align: center;
  padding: 3rem 2rem;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
}

.submit-claim-theme .confirmation-card h2 {
  color: var(--primary-color);
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.submit-claim-theme .confirmation-card .claim-id {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-color);
  margin: 1.5rem 0;
  padding: 0.75rem 1.5rem;
  background-color: #fff7ed;
  border-radius: 12px;
  display: inline-block;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
}

.submit-claim-theme .confirmation-card p {
  color: var(--font-color);
  margin-bottom: 2rem;
  font-size: 1.125rem;
  line-height: 1.6;
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
}

/* Additional customer-friendly elements */

/* Section dividers */
.submit-claim-theme hr {
  border: none;
  height: 1px;
  background-color: #fce7f3;
  margin: 2rem 0;
}

/* Help text */
.submit-claim-theme .help-text {
  color: var(--font-lighter);
  font-size: 0.875rem;
  margin-top: 0.375rem;
  display: block;
}

/* Required field indicator */
.submit-claim-theme .required-field::after {
  content: "*";
  color: var(--accent-color);
  margin-left: 0.25rem;
}

/* Form sections */
.submit-claim-theme .form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #fce7f3;
}

.submit-claim-theme .form-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1.25rem;
}

/* Friendly select dropdown */
.submit-claim-theme select,
.submit-claim-theme .select-trigger {
  appearance: none;
  background-color: white;
  border: 1.5px solid #fce7f3;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  padding-right: 2.5rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--font-color);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ec4899' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.submit-claim-theme select:focus,
.submit-claim-theme .select-trigger:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.15);
  outline: none;
}

/* Select dropdown content styling */
.submit-claim-theme [data-slot="select-content"],
.submit-claim-dropdown {
  background-color: white !important;
  border: 1.5px solid #fce7f3 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.08) !important;
  overflow: hidden !important;
  z-index: 100 !important;
  margin-top: 4px !important;
  padding: 0.5rem !important;
  animation: selectContentFadeIn 0.2s ease !important;
}

@keyframes selectContentFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.submit-claim-theme [data-slot="select-item"],
.submit-claim-dropdown [data-slot="select-item"] {
  color: var(--font-color) !important;
  font-size: 1rem !important;
  padding: 0.6rem 0.75rem !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  margin-bottom: 2px !important;
}

.submit-claim-theme [data-slot="select-item"]:hover,
.submit-claim-dropdown [data-slot="select-item"]:hover {
  background-color: var(--primary-lightest) !important;
}

.submit-claim-theme [data-slot="select-item"][data-highlighted],
.submit-claim-dropdown [data-slot="select-item"][data-highlighted] {
  background-color: var(--primary-lightest) !important;
  color: var(--primary-color) !important;
}

.submit-claim-theme [data-slot="select-item"][data-state="checked"],
.submit-claim-dropdown [data-slot="select-item"][data-state="checked"] {
  background-color: var(--primary-lightest) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
}

.submit-claim-theme [data-slot="select-item"] [data-slot="select-item-indicator"],
.submit-claim-dropdown [data-slot="select-item"] [data-slot="select-item-indicator"] {
  color: var(--primary-color) !important;
}

.submit-claim-theme [data-slot="select-scroll-up-button"],
.submit-claim-theme [data-slot="select-scroll-down-button"],
.submit-claim-dropdown [data-slot="select-scroll-up-button"],
.submit-claim-dropdown [data-slot="select-scroll-down-button"] {
  color: var(--primary-color) !important;
  background-color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 1.5rem !important;
}

.submit-claim-theme [data-slot="select-viewport"],
.submit-claim-dropdown [data-slot="select-viewport"] {
  padding: 0.25rem !important;
}

/* File list styling */
.submit-claim-theme .file-list {
  margin-top: 1rem;
}

.submit-claim-theme .file-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #fdf2f8;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border: 1px solid #fce7f3;
}

.submit-claim-theme .file-name {
  flex: 1;
  font-size: 0.875rem;
  color: var(--font-color);
  margin-right: 1rem;
}

.submit-claim-theme .file-size {
  font-size: 0.75rem;
  color: var(--font-lightest);
  margin-right: 1rem;
}

.submit-claim-theme .file-remove {
  color: var(--font-lightest);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.submit-claim-theme .file-remove:hover {
  color: #ef4444;
  background-color: #fee2e2;
}
