/* Fix for Select dropdown background transparency */
[data-slot="select-content"] {
  background-color: white !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Ensure the viewport has a solid background too */
[data-slot="select-content"] [data-slot="select-viewport"] {
  background-color: white !important;
}

/* Make sure select items have a solid background */
[data-slot="select-item"] {
  background-color: transparent !important;
}

[data-slot="select-item"]:hover,
[data-slot="select-item"][data-highlighted] {
  background-color: #f3f4f6 !important;
}
