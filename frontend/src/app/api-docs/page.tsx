"use client";

import dynamic from "next/dynamic";
import "swagger-ui-react/swagger-ui.css";
import { useEffect, useState } from "react";

const SwaggerUI = dynamic(() => import("swagger-ui-react"), { ssr: false });

export default function ApiDocsPage() {
  const [spec, setSpec] = useState<object | null>(null);

  useEffect(() => {
    fetch("/api/docs")
      .then((res) => res.json())
      .then(setSpec);
  }, []);

  if (!spec) return <div>Loading OpenAPI spec...</div>;

  return (
    <div style={{ minHeight: 600 }}>
      <SwaggerUI spec={spec} />
    </div>
  );
}