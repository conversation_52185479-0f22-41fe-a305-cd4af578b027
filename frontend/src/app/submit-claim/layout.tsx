"use client";

import { useEffect, useState } from "react";
import { usePageStore } from "@/store/usePageStore";

export default function SubmitClaimLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { setClaimDetailPageOpen, setSubmitClaimPageOpen } = usePageStore();
  const [mounted, setMounted] = useState(false);

  // Handle client-side hydration
  useEffect(() => {
    setMounted(true);

    // Set the submit claim page as open when this layout mounts (after hydration)
    setSubmitClaimPageOpen(true);
    setClaimDetailPageOpen(false);

    // Clean up when layout unmounts
    return () => {
      setSubmitClaimPageOpen(false);
    };
  }, [setSubmitClaimPageOpen, setClaimDetailPageOpen]);

  return <>{children}</>;
}
