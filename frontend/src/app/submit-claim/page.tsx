"use client";

import { useForm } from "react-hook-form";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileDropzone } from "@/components/ui/file-dropzone";
import { useState } from "react";
import { FileValidationOptions } from "@/lib/utils";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ClaimFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  employerName: string;
  groupId: string;
  memberId: string;
  claimType: string;
  description: string;
  incidentDate: string;
  document: FileList;
}

export default function SubmitClaimPage() {
  const form = useForm<ClaimFormValues>({
    defaultValues: {
      firstName: "Jordan",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      phone: "************",
      address: "Sample Location, Kansas City, MO 64111",
      employerName: "ABC Association",
      groupId: "12345",
      memberId: "100100",
      claimType: "",
      description: "",
      incidentDate: "",
      document: undefined as unknown as FileList,
    },
  });

  const [files, setFiles] = useState<File[]>([]);
  const [fileError, setFileError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [successMsg, setSuccessMsg] = useState<string | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [claimId, setClaimId] = useState<number | null>(null);
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = form;

  const fileValidationOptions: FileValidationOptions = {
    allowedTypes: [
      "application/pdf",
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/gif",
    ],
    maxSizeMB: 25,
    maxFiles: 10,
    minFiles: 1,
    allowDuplicates: false,
  };

  const onSubmit = async (data: ClaimFormValues) => {
    setSuccessMsg(null);
    setErrorMsg(null);
    if (files.length === 0) {
      setFileError("At least one document is required");
      return;
    }
    setFileError(null);
    setUploading(true);
    setUploadProgress(null);
    try {
      const formData = new FormData();
      files.forEach((file) => formData.append("files", file));
      // Use fetch with progress tracking
      const xhr = new XMLHttpRequest();
      xhr.open("POST", "/api/upload");
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          setUploadProgress(Math.round((event.loaded / event.total) * 100));
        }
      };
      interface UploadedFile {
        originalname: string;
        filename: string;
        path: string;
        size: number;
        mimetype: string;
      }

      const uploadPromise = new Promise<{ files: UploadedFile[] }>(
        (resolve, reject) => {
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve(JSON.parse(xhr.responseText));
            } else {
              reject(new Error("Upload failed"));
            }
          };
          xhr.onerror = () => reject(new Error("Upload failed"));
        },
      );
      xhr.send(formData);
      const uploadResult = await uploadPromise;
      setUploadProgress(null);
      // Now submit the rest of the form
      const claimRes = await fetch("/api/submit-claim", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          address: data.address,
          employerName: data.employerName,
          groupId: data.groupId,
          memberId: data.memberId,
          claimType: data.claimType,
          description: data.description,
          incidentDate: data.incidentDate,
          files: uploadResult.files,
        }),
      });

      const responseData = await claimRes.json();

      if (!claimRes.ok) {
        throw new Error(responseData.error || "Failed to save claim");
      }
      const submittedClaimId = responseData.claim?.id;

      setClaimId(submittedClaimId);
      setShowConfirmation(true);
      setSuccessMsg(
        `Thanks for submitting the claim. Your claim #${submittedClaimId} has been received. We will be in touch shortly.`,
      );
      toast.success(`Claim #${submittedClaimId} submitted successfully!`);
      setFiles([]);
      reset();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Upload failed";
      setErrorMsg(errorMessage);
      toast.error(errorMessage);
    } finally {
      setUploading(false);
      setUploadProgress(null);
    }
  };

  // Confirmation component to show after successful submission
  const ConfirmationCard = () => (
    <Card className="w-full max-w-xl">
      <CardHeader>
        <CardTitle className="text-green-600">
          Claim Submitted Successfully
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <p className="text-lg font-medium mb-2">
            Thanks for submitting the claim.
          </p>
          <p className="text-xl font-bold mb-2">Claim #{claimId}</p>
          <p>We will be in touch shortly.</p>
        </div>
        <Button
          onClick={() => {
            setShowConfirmation(false);
            setClaimId(null);
            setSuccessMsg(null);
          }}
          className="w-full"
        >
          Submit Another Claim
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      {showConfirmation && claimId ? (
        <ConfirmationCard />
      ) : (
        <Card className="w-full max-w-xl">
          <CardHeader>
            <CardTitle>Submit Claim</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...register("firstName", {
                          required: "First name is required",
                        })}
                      />
                    </FormControl>
                    <FormMessage>{errors.firstName?.message}</FormMessage>
                  </FormItem>
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...register("lastName", {
                          required: "Last name is required",
                        })}
                      />
                    </FormControl>
                    <FormMessage>{errors.lastName?.message}</FormMessage>
                  </FormItem>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        {...register("email", {
                          required: "Email is required",
                        })}
                      />
                    </FormControl>
                    <FormMessage>{errors.email?.message}</FormMessage>
                  </FormItem>
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input type="tel" {...register("phone")} />
                    </FormControl>
                    <FormMessage>{errors.phone?.message}</FormMessage>
                  </FormItem>
                </div>

                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea {...register("address")} />
                  </FormControl>
                  <FormMessage>{errors.address?.message}</FormMessage>
                </FormItem>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormItem>
                    <FormLabel>Employer Name</FormLabel>
                    <FormControl>
                      <Input type="text" {...register("employerName")} />
                    </FormControl>
                    <FormMessage>{errors.employerName?.message}</FormMessage>
                  </FormItem>
                  <FormItem>
                    <FormLabel>Group ID</FormLabel>
                    <FormControl>
                      <Input type="text" {...register("groupId")} />
                    </FormControl>
                    <FormMessage>{errors.groupId?.message}</FormMessage>
                  </FormItem>
                  <FormItem>
                    <FormLabel>Member ID</FormLabel>
                    <FormControl>
                      <Input type="text" {...register("memberId")} />
                    </FormControl>
                    <FormMessage>{errors.memberId?.message}</FormMessage>
                  </FormItem>
                </div>

                <hr className="my-4 border-gray-200" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem>
                    <FormLabel>Claim Type</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) =>
                          form.setValue("claimType", value)
                        }
                        defaultValue=""
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select claim type" />
                        </SelectTrigger>
                        <SelectContent className="submit-claim-dropdown">
                          <SelectItem value="Critical Illness">
                            Critical Illness
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage>{errors.claimType?.message}</FormMessage>
                  </FormItem>
                  <FormItem>
                    <FormLabel>Date of Incident</FormLabel>
                    <FormControl>
                      <Input type="date" {...register("incidentDate")} />
                    </FormControl>
                    <FormMessage>{errors.incidentDate?.message}</FormMessage>
                  </FormItem>
                </div>

                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...register("description", {
                        required: "Description is required",
                      })}
                    />
                  </FormControl>
                  <FormMessage>{errors.description?.message}</FormMessage>
                </FormItem>
                <FormItem>
                  <FormLabel>Supporting Documents</FormLabel>
                  <FormControl>
                    <FileDropzone
                      files={files}
                      onChange={setFiles}
                      accept={fileValidationOptions.allowedTypes?.join(",")}
                      maxFiles={fileValidationOptions.maxFiles}
                      onError={(errs) =>
                        setFileError(errs.length > 0 ? errs[0] : null)
                      }
                      validationOptions={fileValidationOptions}
                    />
                  </FormControl>
                  <FormMessage>{fileError}</FormMessage>
                </FormItem>
                {uploading && (
                  <div className="w-full bg-gray-200 rounded h-2 mb-2">
                    <div
                      className="bg-blue-500 h-2 rounded"
                      style={{ width: `${uploadProgress ?? 0}%` }}
                    />
                  </div>
                )}
                {successMsg && (
                  <div className="text-green-600 text-sm">{successMsg}</div>
                )}
                {errorMsg && (
                  <div className="text-red-600 text-sm">{errorMsg}</div>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting || uploading}
                  className="w-full"
                >
                  {uploading
                    ? "Uploading..."
                    : isSubmitting
                      ? "Submitting..."
                      : "Submit Claim"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
