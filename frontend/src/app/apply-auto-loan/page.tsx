"use client";

import { useForm } from "react-hook-form";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileDropzone } from "@/components/ui/file-dropzone";
import { useState } from "react";
import React from "react";
import { FileValidationOptions } from "@/lib/utils";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePageStore } from "@/store/usePageStore";

interface AutoLoanFormValues {
  // Personal details
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  dniNumber: string;
  dateOfBirth: string;
  incomeSource: string;
  // Finance details
  loanAmount: number;
  totalPrice: number;
  tradeInValue: number;
  downPayment: number;
  remainingPrice: number;
  // Car details
  niv: string;
  newPreowned: string;
  year: number;
  make: string;
  model: string;
  trim: string;
  // Documents
  document: FileList;
}

export default function ApplyAutoLoanPage() {
  const form = useForm<AutoLoanFormValues>({
    defaultValues: {
      // Personal details with default values for testing
      firstName: "RAMÓN",
      lastName: "GOMEZ DE LA CRUZ",
      email: "<EMAIL>",
      phone: "678 159 753",
      address: "Avenida de Concha Espina, 1, 28036 Madrid, Spain",
      dniNumber: "01234567A",
      dateOfBirth: "1977-05-22",
      incomeSource: "Salaried",
      // Finance details
      loanAmount: 10000,
      totalPrice: 20000,
      tradeInValue: 10000,
      downPayment: 0,
      remainingPrice: 10000,
      // Car details
      niv: "01234567A",
      newPreowned: "New",
      year: 2023,
      make: "Volkswagen",
      model: "Golf",
      trim: "SE",
      document: undefined as unknown as FileList,
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = form;
  const [files, setFiles] = useState<File[]>([]);
  const [fileErrors, setFileErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [autoLoanId, setAutoLoanId] = useState<number | null>(null);
  const { setSubmitClaimPageOpen } = usePageStore();

  // Watch finance fields to calculate remaining price
  const loanAmount = watch("loanAmount");
  const totalPrice = watch("totalPrice");
  const tradeInValue = watch("tradeInValue");
  const downPayment = watch("downPayment");

  // Auto-calculate remaining price
  React.useEffect(() => {
    const remaining = totalPrice - tradeInValue - downPayment;
    setValue("remainingPrice", remaining);
    setSubmitClaimPageOpen(true);
  }, [totalPrice, tradeInValue, downPayment, setValue]);

  const fileValidationOptions: FileValidationOptions = {
    maxSizeMB: 10, // 10MB
    allowedTypes: ["image/jpeg", "image/png", "application/pdf", "image/jpg"],
    maxFiles: 10,
  };

  const onSubmit = async (data: AutoLoanFormValues) => {
    if (files.length === 0) {
      toast.error("Please upload at least one document");
      return;
    }

    setIsSubmitting(true);
    setUploadProgress(0);

    try {
      // First upload files
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      const uploadPromise = new Promise<{ files: any[] }>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(progress);
          }
        });
        xhr.addEventListener("load", () => {
          if (xhr.status === 200) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            reject(new Error("Upload failed"));
          }
        });
        xhr.addEventListener("error", () => reject(new Error("Upload failed")));
        xhr.open("POST", "/api/upload");
        xhr.send(formData);
      });

      const uploadResult = await uploadPromise;
      setUploadProgress(null);

      // Now submit the auto loan application
      const autoLoanRes = await fetch("/api/submit-auto-loan", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...data,
          files: uploadResult.files,
        }),
      });

      const responseData = await autoLoanRes.json();

      if (!autoLoanRes.ok) {
        throw new Error(
          responseData.error || "Failed to submit auto loan application",
        );
      }

      setAutoLoanId(responseData.autoLoan.id);
      setShowConfirmation(true);
      toast.success("Auto loan application submitted successfully!");
    } catch (error) {
      console.error("Error submitting auto loan application:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to submit application",
      );
    } finally {
      setIsSubmitting(false);
      setUploadProgress(null);
    }
  };

  const ConfirmationCard = () => (
    <Card className="w-full max-w-xl">
      <CardHeader>
        <CardTitle className="text-green-600">
          Application Submitted Successfully!
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-4">
          Your auto loan application has been submitted successfully.
        </p>
        <p className="text-sm text-gray-500 mb-4">
          Application ID: {autoLoanId}
        </p>
        <Button
          onClick={() => {
            setShowConfirmation(false);
            form.reset();
            setFiles([]);
            setAutoLoanId(null);
          }}
          className="w-full"
        >
          Submit Another Application
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      {showConfirmation && autoLoanId ? (
        <ConfirmationCard />
      ) : (
        <Card className="w-full max-w-4xl">
          <CardHeader>
            <CardTitle>Apply for Auto Loan</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                {/* Personal Details Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">
                    Personal Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("firstName", {
                            required: "First name is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.firstName?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("lastName", {
                            required: "Last name is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.lastName?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          {...register("email", {
                            required: "Email is required",
                            pattern: {
                              value: /^\S+@\S+$/i,
                              message: "Invalid email address",
                            },
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.email?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          {...register("phone", {
                            required: "Phone number is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.phone?.message}</FormMessage>
                    </FormItem>
                    <FormItem className="md:col-span-2">
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("address", {
                            required: "Address is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.address?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>DNI Number</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("dniNumber", {
                            required: "DNI number is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.dniNumber?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Date of Birth</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...register("dateOfBirth", {
                            required: "Date of birth is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.dateOfBirth?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Income Source</FormLabel>
                      <Select
                        {...register("incomeSource", {
                          required: "Income source is required",
                        })}
                        onValueChange={(value) =>
                          setValue("incomeSource", value)
                        }
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select income source" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Salaried">Salaried</SelectItem>
                          <SelectItem value="Self-Employed">
                            Self-Employed
                          </SelectItem>
                          <SelectItem value="Pension">Pension</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage>{errors.incomeSource?.message}</FormMessage>
                    </FormItem>
                  </div>
                </div>

                {/* Finance Details Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">
                    Finance Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem>
                      <FormLabel>Loan Amount (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...register("loanAmount", {
                            required: "Loan amount is required",
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.loanAmount?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Total Price (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...register("totalPrice", {
                            required: "Total price is required",
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.totalPrice?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Trade In Value (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...register("tradeInValue", {
                            required: "Trade in value is required",
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.tradeInValue?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Down Payment (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...register("downPayment", {
                            required: "Down payment is required",
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.downPayment?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Remaining Price (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...register("remainingPrice", {
                            valueAsNumber: true,
                          })}
                          readOnly
                          className="bg-gray-100"
                        />
                      </FormControl>
                      <FormMessage>
                        {errors.remainingPrice?.message}
                      </FormMessage>
                    </FormItem>
                  </div>
                </div>

                {/* Car Details Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">
                    Car Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem>
                      <FormLabel>NIV</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("niv", {
                            required: "NIV is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.niv?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>New/Preowned</FormLabel>
                      <Select
                        {...register("newPreowned", {
                          required: "New/Preowned is required",
                        })}
                        onValueChange={(value) =>
                          setValue("newPreowned", value)
                        }
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select condition" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="New">New</SelectItem>
                          <SelectItem value="Preowned">Preowned</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage>{errors.newPreowned?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Year</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...register("year", {
                            required: "Year is required",
                            valueAsNumber: true,
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.year?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Make</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("make", {
                            required: "Make is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.make?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Model</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("model", {
                            required: "Model is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.model?.message}</FormMessage>
                    </FormItem>
                    <FormItem>
                      <FormLabel>Trim</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...register("trim", {
                            required: "Trim is required",
                          })}
                        />
                      </FormControl>
                      <FormMessage>{errors.trim?.message}</FormMessage>
                    </FormItem>
                  </div>
                </div>

                {/* Documents Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">
                    Documents
                  </h3>
                  <FormItem>
                    <FormLabel>Upload Documents</FormLabel>
                    <FormControl>
                      <FileDropzone
                        onChange={setFiles}
                        files={files}
                        accept=".pdf,.jpg,.jpeg,.png"
                        maxFiles={10}
                        onError={setFileErrors}
                        validationOptions={fileValidationOptions}
                      />
                    </FormControl>
                    {fileErrors.length > 0 && (
                      <div className="text-red-500 text-sm">
                        {fileErrors.map((error, index) => (
                          <div key={index}>{error}</div>
                        ))}
                      </div>
                    )}
                    <FormMessage>{errors.document?.message}</FormMessage>
                  </FormItem>
                </div>

                {/* Progress Bar */}
                {uploadProgress !== null && (
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting
                    ? "Submitting..."
                    : "Submit Auto Loan Application"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
