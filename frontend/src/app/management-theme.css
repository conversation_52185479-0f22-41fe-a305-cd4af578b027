/* Management theme colors - Salesforce Edition */
.management-theme {
  /* Salesforce color palette */
  --primary-color: #0176d3; /* Salesforce blue */
  --accent-color: #b4a0ff; /* Salesforce purple */
  --background-color: #f3f3f3; /* Salesforce light gray background */
  --font-color: #16325c; /* Salesforce dark blue text */

  /* Derived shades for UI elements */
  --primary-light: #0288d1; /* Lighter blue */
  --primary-dark: #014486; /* Darker blue */
  --primary-darker: #032d60; /* Even darker blue */
  --primary-lightest: #e3f2fd; /* Very light blue */

  --accent-light: #c2b5ff; /* Lighter purple */
  --accent-dark: #9f86ff; /* Darker purple */

  --background-darker: #e5e5e5; /* Darker gray */
  --background-darkest: #dddbda; /* Even darker gray */
  --background-lighter: #f8f8f8; /* Lighter gray */

  --font-light: #2a426a; /* Lighter text */
  --font-lighter: #54698d; /* Even lighter text */
  --font-lightest: #747474; /* Lightest text for secondary info */

  /* Sidebar colors */
  --sidebar-bg: #032d60; /* Salesforce dark blue */
  --sidebar-darker: #01172f; /* Darker blue */
  --sidebar-lighter: #054ada; /* Lighter blue */
  --sidebar-text: #ffffff; /* White text */
  --sidebar-accent: #b4a0ff; /* Purple accent */
  --sidebar-hover: rgba(255, 255, 255, 0.1); /* Hover state */

  /* System colors */
  --background: 0 0% 95%; /* Light gray in HSL */
  --foreground: 220 50% 22%; /* Dark blue in HSL */

  /* Card styling */
  --card: 0 0% 100%; /* White in HSL */
  --card-foreground: 220 50% 22%; /* Dark blue in HSL */

  /* Popover styling */
  --popover: 0 0% 100%; /* White in HSL */
  --popover-foreground: 220 50% 22%; /* Dark blue in HSL */

  /* Primary action styling */
  --primary: 205 100% 41%; /* Salesforce blue in HSL */
  --primary-foreground: 0 0% 100%; /* White in HSL */

  /* Secondary styling */
  --secondary: 0 0% 90%; /* Light gray in HSL */
  --secondary-foreground: 220 50% 22%; /* Dark blue in HSL */

  /* Muted styling */
  --muted: 0 0% 96%; /* Very light gray in HSL */
  --muted-foreground: 220 10% 46%; /* Muted blue in HSL */

  /* Accent styling */
  --accent: 252 100% 81%; /* Purple in HSL */
  --accent-foreground: 220 50% 22%; /* Dark blue in HSL */

  /* Destructive styling - keeping this for error states */
  --destructive: 0 65% 52%; /* Salesforce red in HSL */
  --destructive-foreground: 0 0% 100%; /* White in HSL */

  /* Borders and input styling */
  --border: 0 0% 85%; /* Light gray border in HSL */
  --input: 0 0% 85%; /* Light gray input in HSL */
  --ring: 205 100% 41%; /* Salesforce blue in HSL */
}

/* Management theme specific styles */
.management-theme body {
  background-color: var(--background-color);
  color: var(--font-color);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.management-theme .card {
  border: 1px solid #dddbda; /* Salesforce standard border color */
  background-color: white;
  color: var(--font-color);
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  border-radius: 4px; /* Salesforce uses 4px border radius */
}

.management-theme .card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0; /* No top border in Salesforce style */
  opacity: 0;
  transition: opacity 0.2s ease;
}

.management-theme .card:hover {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.16);
  transform: translateY(-1px);
}

.management-theme .card:hover::before {
  opacity: 0; /* No hover effect on top border */
}

/* Card content styling - Salesforce style */
.management-theme [data-slot="card-content"] {
  position: relative;
  z-index: 1;
  padding: 1rem;
}

.management-theme [data-slot="card-header"] {
  position: relative;
  z-index: 1;
  padding: 0.75rem 1rem;
  background-color: #f3f3f3; /* Light gray header background */
  border-bottom: 1px solid #dddbda;
  border-radius: 4px 4px 0 0;
}

.management-theme [data-slot="card-footer"] {
  position: relative;
  z-index: 1;
  border-top: 1px solid #dddbda;
  margin-top: 0;
  padding: 0.75rem 1rem;
  background-color: #f3f3f3; /* Light gray footer background */
  border-radius: 0 0 4px 4px;
}

.management-theme button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.5;
  transition: all 0.1s linear;
  box-shadow: none;
}

.management-theme button:hover {
  background-color: var(--primary-dark);
  transform: none;
  box-shadow: none;
}

.management-theme button:active {
  transform: none;
  background-color: var(--primary-darker);
}

/* Salesforce style outline button */
.management-theme button[variant="outline"] {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid #dddbda;
}

.management-theme button[variant="outline"]:hover {
  background-color: #f3f3f3;
  color: var(--primary-dark);
}

.management-theme input {
  background-color: white;
  color: var(--font-color);
  border: 1px solid #dddbda;
  border-radius: 4px;
  padding: 0.5rem;
  height: 2.25rem;
  font-size: 0.8125rem;
  line-height: 1.5;
  transition:
    border-color 0.1s linear,
    box-shadow 0.1s linear;
}

.management-theme input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 3px #0176d3;
  outline: none;
}

.management-theme input::placeholder {
  color: #747474;
  font-size: 0.8125rem;
}

.management-theme table {
  border: 1px solid #dddbda;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
}

.management-theme table thead {
  background-color: #f3f3f3; /* Salesforce uses light gray for table headers */
  color: var(--font-color);
  font-weight: 700;
}

.management-theme table thead th {
  color: var(--font-color) !important;
  font-weight: 700;
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #dddbda;
  text-align: left;
  white-space: nowrap;
  vertical-align: middle;
}

.management-theme table tbody {
  background-color: white;
}

.management-theme table tbody tr {
  border-color: #dddbda;
  transition: background-color 0.1s linear;
}

.management-theme table tbody tr:hover {
  background-color: #f3f3f3;
}

.management-theme table td {
  color: var(--font-color) !important;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #dddbda;
  font-size: 0.8125rem;
  line-height: 1.5;
  vertical-align: middle;
}

.management-theme table tbody tr:last-child td {
  border-bottom: none;
}

/* Status colors for management theme - Salesforce style */
.management-theme .status-submitted {
  background-color: #f2f2f2;
  color: #16325c;
  border: 1px solid #dddbda;
  padding: 0.25rem 0.5rem;
  border-radius: 15rem; /* Salesforce uses fully rounded pills */
  font-size: 0.75rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  line-height: 1.25;
  text-transform: uppercase;
  letter-spacing: 0.0625em;
}

.management-theme .status-in-review {
  background-color: #fff03f; /* Salesforce yellow */
  color: #514f00;
  border: 1px solid #e3d636;
  padding: 0.25rem 0.5rem;
  border-radius: 15rem;
  font-size: 0.75rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  line-height: 1.25;
  text-transform: uppercase;
  letter-spacing: 0.0625em;
}

.management-theme .status-approved {
  background-color: #4bca81; /* Salesforce green */
  color: white;
  border: 1px solid #36b66c;
  padding: 0.25rem 0.5rem;
  border-radius: 15rem;
  font-size: 0.75rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  line-height: 1.25;
  text-transform: uppercase;
  letter-spacing: 0.0625em;
}

.management-theme .status-rejected {
  background-color: #ea001e; /* Salesforce red */
  color: white;
  border: 1px solid #c9000e;
  padding: 0.25rem 0.5rem;
  border-radius: 15rem;
  font-size: 0.75rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  line-height: 1.25;
  text-transform: uppercase;
  letter-spacing: 0.0625em;
}

/* Additional professional styling - Salesforce style */
.management-theme select,
.management-theme .select-trigger {
  background-color: white;
  border: 1px solid #dddbda;
  border-radius: 4px;
  color: var(--font-color);
  padding: 0 0.5rem;
  height: 2.25rem;
  font-size: 0.8125rem;
  line-height: 1.5;
  transition:
    border-color 0.1s linear,
    box-shadow 0.1s linear;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2316325c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1rem;
  padding-right: 2rem;
}

.management-theme select:focus,
.management-theme .select-trigger:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 3px #0176d3;
  outline: none;
}

/* Fix for description field readability - Salesforce style */
.management-theme .claim-description-container,
.management-theme .border.p-4.rounded-md.bg-gray-50,
.management-theme
  p.mt-2.text-sm.whitespace-pre-wrap.border.p-3.rounded-md.bg-gray-50 {
  background-color: white !important;
  color: var(--font-color) !important;
  border: 1px solid #dddbda !important;
  border-radius: 4px !important;
  padding: 0.75rem !important;
  box-shadow: none !important;
}

.management-theme .claim-description-container p,
.management-theme .border.p-4.rounded-md.bg-gray-50 p,
.management-theme
  p.mt-2.text-sm.whitespace-pre-wrap.border.p-3.rounded-md.bg-gray-50 {
  color: var(--font-color) !important;
  font-size: 0.8125rem !important;
  line-height: 1.5 !important;
}

.management-theme .badge {
  font-weight: 700;
  font-size: 0.75rem;
  letter-spacing: 0.0625em;
  text-transform: uppercase;
  padding: 0.25rem 0.5rem;
  border-radius: 15rem;
  background-color: #f2f2f2;
  color: #16325c;
  border: 1px solid #dddbda;
  line-height: 1.25;
}

.management-theme h1 {
  font-weight: 700;
  font-size: 1.75rem;
  line-height: 1.25;
  color: var(--font-color);
  margin-bottom: 1rem;
}

.management-theme h2 {
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.25;
  color: var(--font-color);
  margin-bottom: 0.75rem;
}

.management-theme h3,
.management-theme h4 {
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--font-color);
  margin-bottom: 0.5rem;
}

.management-theme .card-title {
  color: var(--font-color);
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.5;
  position: relative;
  padding-bottom: 0;
  margin-bottom: 0;
}

.management-theme .card-title::after {
  content: none; /* Remove the underline effect */
}

.management-theme .card:hover .card-title::after {
  width: 0;
}

/* Accent color elements - Salesforce style */
.management-theme .accent-element {
  color: var(--accent-color);
}

.management-theme button.accent {
  background-color: #706e6b; /* Salesforce neutral button */
  border: none;
  color: white;
}

.management-theme button.accent:hover {
  background-color: #514f4d; /* Darker neutral */
}

/* Salesforce style secondary button */
.management-theme button.secondary {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid #dddbda;
}

.management-theme button.secondary:hover {
  background-color: #f3f3f3;
}

/* Scrollbar styling - Salesforce style */
.management-theme ::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

.management-theme ::-webkit-scrollbar-track {
  background: #f3f3f3;
  border-radius: 4px;
}

.management-theme ::-webkit-scrollbar-thumb {
  background: #dddbda;
  border-radius: 4px;
}

.management-theme ::-webkit-scrollbar-thumb:hover {
  background: #b0adab; /* Salesforce neutral gray */
}

/* Sidebar styling - Salesforce style */
.management-theme .sidebar {
  background-color: #032d60; /* Salesforce dark blue */
  color: white;
  border-left: none;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.16);
}

.management-theme .sidebar-icon {
  color: white;
  transition: all 0.1s linear;
  border-radius: 0;
  padding: 0.75rem;
}

.management-theme .sidebar-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: none;
}

.management-theme .sidebar-icon.active {
  background-color: #0070d2; /* Salesforce blue */
  color: white;
  border-left: 4px solid white;
}

.management-theme .sidebar-panel {
  background-color: white;
  color: #16325c; /* Salesforce dark blue text */
  border-left: 1px solid #dddbda;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.16);
}

.management-theme .sidebar-panel h2 {
  color: #16325c;
  font-size: 1rem;
  font-weight: 700;
  border-bottom: 1px solid #dddbda;
  padding: 0.75rem 1rem;
  margin: 0 0 1rem 0;
  background-color: #f3f3f3;
}
