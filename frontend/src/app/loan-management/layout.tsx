"use client";

import { usePathname } from "next/navigation";
import { DetailSidebar } from "@/components/detail-sidebar";
import { usePageStore } from "@/store/usePageStore";
import { useEffect, useState } from "react";

export default function LoanManagementLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { isLoanDetailPageOpen, setLoanDetailPageOpen, setSubmitClaimPageOpen } = usePageStore();
  const [mounted, setMounted] = useState(false);

  // Handle client-side hydration and pathname changes in a single effect
  useEffect(() => {
    // Set mounted state to true on first render
    if (!mounted) {
      setMounted(true);
    }

    // Skip the rest of the effect if not mounted yet
    if (!mounted && pathname) return;

    // Determine if we're on a detail page
    const isDetailPage =
      pathname?.includes("/loan-management/") &&
      !pathname?.endsWith("/loan-management/");

    // Check if the state needs to be updated to avoid unnecessary renders
    if (isDetailPage !== isLoanDetailPageOpen) {
      console.log(`Layout: Setting loan detail page open to ${isDetailPage}`);
      setLoanDetailPageOpen(isDetailPage);
    }

    // Always ensure submit claim page is false when in loan management
    setSubmitClaimPageOpen(false);
  }, [pathname, mounted, isLoanDetailPageOpen, setLoanDetailPageOpen, setSubmitClaimPageOpen]);

  // During SSR or before hydration, render a simpler version
  if (!mounted) {
    return (
      <div className="relative">
        <div className="w-full">
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${isLoanDetailPageOpen ? "flex" : ""}`}>
      {/* Main content area - takes full width minus sidebar width when sidebar is present */}
      <div className={`${isLoanDetailPageOpen ? "flex-1 mr-16" : "w-full"}`}>
        {children}
      </div>

      {/* Fixed width sidebar area - only on detail pages */}
      {isLoanDetailPageOpen && (
        <div className="w-16 flex-shrink-0">
          <DetailSidebar />
        </div>
      )}
    </div>
  );
}
