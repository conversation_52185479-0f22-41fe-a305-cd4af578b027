/**
 * Airtable data models
 *
 * This file defines the types for Airtable tables and records.
 */

/**
 * Base interface for all Airtable records
 */
export interface AirtableBase {
  id: string;
  createdTime: string;
}

/**
 * Employee record in Airtable
 */
export interface AirtableEmployee {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  employerName?: string;
  groupId?: string;
  memberId?: string;
  notes?: string;
}

/**
 * Claim record in Airtable
 */
export interface AirtableClaim {
  employeeId: string[]; // Array of linked record IDs
  claimType: string;
  description: string;
  incidentDate?: string; // ISO date string
  dateFiled: string; // ISO date string
  status: string;
  notes?: string;
}

/**
 * Document record in Airtable
 */
export interface AirtableDocument {
  name: string;
  url: string;
  type: string;
  claimId?: string[]; // Array of linked record IDs
  policyId?: string[]; // Array of linked record IDs
  uploadedAt: string; // ISO date string
}

/**
 * Policy record in Airtable
 */
export interface AirtablePolicy {
  employeeId: string[]; // Array of linked record IDs
  policyOwner: string;
  insured: string;
  spouse?: string;
  group: string;
  policyNumber: string;
  originalEffectiveDate?: string; // ISO date string
  scheduledEffectiveDate?: string; // ISO date string
  issuedAge?: number;
  insuredCoverage?: number;
  spouseCoverage?: number;
  notes?: string;
}

/**
 * Comment record in Airtable
 */
export interface AirtableComment {
  claimId: string[]; // Array of linked record IDs
  text: string;
  createdAt: string; // ISO date string
  author?: string;
}

/**
 * Claim Note record in Airtable
 */
export interface AirtableClaimNote {
  "Claim ID": number;
  Notes: string;
  created_by: "system" | "user";
  created_at: string; // ISO date string with time and timezone
  "Note Type": "Process" | "User" | "Feedback" | "NaN";
}

/**
 * Full Status record with Airtable metadata
 */
export interface AirtableClaimStatus {
  "Claim ID": number;
  Status: string;
  Notes: string;
}

/**
 * Full Status record with Airtable metadata
 */
export interface AirtableDocumentStatus {
  "Claim ID": number;
  "Document ID": number;
  Status: string;
  Classification: string;
}

/**
 * Full Status record with Airtable metadata
 */
export interface AirtableDocumentStatusRecord extends AirtableBase {
  fields: AirtableDocumentStatus;
}

/**
 * Full Status record with Airtable metadata
 */
export interface AirTableClaimStatusRecord extends AirtableBase {
  fields: AirtableClaimStatus;
}

/**
 * Full Employee record with Airtable metadata
 */
export interface AirtableEmployeeRecord extends AirtableBase {
  fields: AirtableEmployee;
}

/**
 * Full Claim record with Airtable metadata
 */
export interface AirtableClaimRecord extends AirtableBase {
  fields: AirtableClaim;
}

/**
 * Full Document record with Airtable metadata
 */
export interface AirtableDocumentRecord extends AirtableBase {
  fields: AirtableDocument;
}

/**
 * Full Policy record with Airtable metadata
 */
export interface AirtablePolicyRecord extends AirtableBase {
  fields: AirtablePolicy;
}

/**
 * Full Comment record with Airtable metadata
 */
export interface AirtableCommentRecord extends AirtableBase {
  fields: AirtableComment;
}

/**
 * Full Claim Note record with Airtable metadata
 */
export interface AirtableClaimNoteRecord extends AirtableBase {
  fields: AirtableClaimNote;
}

/**
 * Table names in Airtable
 */
export enum AirtableTable {
  EMPLOYEES = "Employees",
  CLAIMS = "Claims",
  DOCUMENTS = "Documents",
  POLICIES = "Policies",
  COMMENTS = "Comments",
  STATUS = "Claims Status",
  DOCUMENT_STATUS = "Attached Document Status",
  CLAIM_NOTES = "Claim Notes",
}
