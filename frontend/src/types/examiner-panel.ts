// Types for the examiner panel data

export interface DecisionSupportItem {
  label: string;
  value: string;
}

export interface ActionItem {
  id: number;
  title: string;
  status: "green" | "yellow" | "red";
  decisionSupport: DecisionSupportItem[];
}

export interface GamePlanItem {
  text: string;
}

export interface SummaryItem {
  timestamp: string;
  source: string;
  text: string;
}

export interface ClaimDocument {
  id: string;
  createdTime: string;
  "Document ID": number;
  Status: string;
  Classification: string;
}

export interface ClaimNote {
  id: string;
  createdTime: string;
  "Claim ID": number;
  Notes: string;
  created_by: string;
  created_at: string;
}

export interface ClaimGameplan {
  id: string;
  createdTime: string;
  "Claim ID": number;
  Notes: string;
  Status: "NaN" | "Generating" | "Done";
}

export interface ClaimActions {
  id: string;
  createdTime: string;
  "Claim ID": number;
  Notes: string;
  Status: "NaN" | "Generating" | "Done";
}

export interface ClaimStatus {
  "Claim ID": number;
  Status: string;
  documents: ClaimDocument[];
  notes: ClaimNote[];
  gameplan: ClaimGameplan;
  actions: ClaimActions;
}

export interface StatusSectionData {
  claimStatus: string;
  documentsCount: number;
  documentsProcessed: number;
  gameplanStatus: string;
  actionsStatus: string;
}

export interface ExaminerPanelData {
  gamePlan: GamePlanItem[];
  actionItems: ActionItem[];
  summary: SummaryItem[];
  claimStatus?: ClaimStatus;
  statusSection?: StatusSectionData;
}
