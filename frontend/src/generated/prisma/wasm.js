
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.EmployeeScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  address: 'address',
  employerName: 'employerName',
  groupId: 'groupId',
  memberId: 'memberId'
};

exports.Prisma.ClaimScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  claimType: 'claimType',
  description: 'description',
  incidentDate: 'incidentDate',
  dateFiled: 'dateFiled',
  status: 'status'
};

exports.Prisma.ClaimDocumentScalarFieldEnum = {
  id: 'id',
  claimId: 'claimId',
  fileName: 'fileName',
  filePath: 'filePath',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.ClaimCommentScalarFieldEnum = {
  id: 'id',
  claimId: 'claimId',
  text: 'text',
  createdAt: 'createdAt'
};

exports.Prisma.PolicyScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  policyOwner: 'policyOwner',
  insured: 'insured',
  spouse: 'spouse',
  group: 'group',
  policyNumber: 'policyNumber',
  originalEffectiveDate: 'originalEffectiveDate',
  scheduledEffectiveDate: 'scheduledEffectiveDate',
  issuedAge: 'issuedAge',
  insuredCoverage: 'insuredCoverage',
  spouseCoverage: 'spouseCoverage'
};

exports.Prisma.PolicyDocumentScalarFieldEnum = {
  id: 'id',
  policyId: 'policyId',
  fileName: 'fileName',
  filePath: 'filePath',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.AutoLoanScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  dniNumber: 'dniNumber',
  dateOfBirth: 'dateOfBirth',
  incomeSource: 'incomeSource',
  loanAmount: 'loanAmount',
  totalPrice: 'totalPrice',
  tradeInValue: 'tradeInValue',
  downPayment: 'downPayment',
  remainingPrice: 'remainingPrice',
  niv: 'niv',
  newPreowned: 'newPreowned',
  year: 'year',
  make: 'make',
  model: 'model',
  trim: 'trim',
  applicationDate: 'applicationDate',
  status: 'status'
};

exports.Prisma.AutoLoanDocumentScalarFieldEnum = {
  id: 'id',
  autoLoanId: 'autoLoanId',
  fileName: 'fileName',
  filePath: 'filePath',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Employee: 'Employee',
  Claim: 'Claim',
  ClaimDocument: 'ClaimDocument',
  ClaimComment: 'ClaimComment',
  Policy: 'Policy',
  PolicyDocument: 'PolicyDocument',
  AutoLoan: 'AutoLoan',
  AutoLoanDocument: 'AutoLoanDocument'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
