import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export interface FileValidationOptions {
  allowedTypes?: string[]; // e.g., ["image/png", "application/pdf"]
  maxSizeMB?: number; // e.g., 5 for 5MB
  maxFiles?: number;
  minFiles?: number;
  allowDuplicates?: boolean;
}

export function validateFiles(
  files: File[],
  options: FileValidationOptions = {},
): string[] {
  const errors: string[] = [];
  const {
    allowedTypes,
    maxSizeMB = 25,
    maxFiles = 10,
    minFiles = 1,
    allowDuplicates = false,
  } = options;

  if (minFiles && files.length < minFiles) {
    errors.push(`At least ${minFiles} file(s) required.`);
  }
  if (maxFiles && files.length > maxFiles) {
    errors.push(`No more than ${maxFiles} file(s) allowed.`);
  }

  const seen = new Set<string>();
  for (const file of files) {
    if (allowedTypes && !allowedTypes.includes(file.type)) {
      errors.push(`File type not allowed: ${file.name}`);
    }
    if (file.size > maxSizeMB * 1024 * 1024) {
      errors.push(`File too large: ${file.name} (max ${maxSizeMB}MB)`);
    }
    if (!allowDuplicates) {
      const key = file.name + file.size;
      if (seen.has(key)) {
        errors.push(`Duplicate file: ${file.name}`);
      }
      seen.add(key);
    }
  }

  return errors;
}
