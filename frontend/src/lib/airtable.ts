/**
 * Airtable API service
 * 
 * This module provides functions to interact with the Airtable API.
 */

/**
 * Configuration for Airtable API
 */
export const airtableConfig = {
  apiKey: process.env.AIRTABLE_API_KEY,
  baseId: process.env.AIRTABLE_BASE_ID,
  apiUrl: 'https://api.airtable.com/v0',
};

/**
 * Generic type for Airtable record
 */
export interface AirtableRecord<T> {
  id: string;
  fields: T;
  createdTime: string;
}

/**
 * Generic type for Airtable response
 */
export interface AirtableResponse<T> {
  records: AirtableRecord<T>[];
  offset?: string;
}

/**
 * Options for fetching records
 */
export interface FetchOptions {
  maxRecords?: number;
  view?: string;
  filterByFormula?: string;
  sort?: Array<{ field: string; direction: 'asc' | 'desc' }>;
  offset?: string;
}

/**
 * Create query string from options
 */
const createQueryString = (options: FetchOptions): string => {
  const params = new URLSearchParams();
  
  if (options.maxRecords) {
    params.append('maxRecords', options.maxRecords.toString());
  }
  
  if (options.view) {
    params.append('view', options.view);
  }
  
  if (options.filterByFormula) {
    params.append('filterByFormula', options.filterByFormula);
  }
  
  if (options.sort) {
    params.append('sort', JSON.stringify(options.sort));
  }
  
  if (options.offset) {
    params.append('offset', options.offset);
  }
  
  return params.toString();
};

/**
 * Fetch records from a table
 */
export async function fetchRecords<T>(
  tableName: string,
  options: FetchOptions = {}
): Promise<AirtableResponse<T>> {
  if (!airtableConfig.apiKey || !airtableConfig.baseId) {
    throw new Error('Airtable API key or base ID is not configured');
  }

  const queryString = createQueryString(options);
  const url = `${airtableConfig.apiUrl}/${airtableConfig.baseId}/${encodeURIComponent(tableName)}?${queryString}`;
  
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${airtableConfig.apiKey}`,
      'Content-Type': 'application/json',
    },
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Airtable API error: ${response.status} ${errorText}`);
  }
  
  return response.json();
}

/**
 * Fetch a single record by ID
 */
export async function fetchRecord<T>(
  tableName: string,
  recordId: string
): Promise<AirtableRecord<T>> {
  if (!airtableConfig.apiKey || !airtableConfig.baseId) {
    throw new Error('Airtable API key or base ID is not configured');
  }

  const url = `${airtableConfig.apiUrl}/${airtableConfig.baseId}/${encodeURIComponent(tableName)}/${recordId}`;
  
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${airtableConfig.apiKey}`,
      'Content-Type': 'application/json',
    },
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Airtable API error: ${response.status} ${errorText}`);
  }
  
  return response.json();
}

/**
 * Create a new record
 */
export async function createRecord<T>(
  tableName: string,
  fields: Partial<T>
): Promise<AirtableRecord<T>> {
  if (!airtableConfig.apiKey || !airtableConfig.baseId) {
    throw new Error('Airtable API key or base ID is not configured');
  }

  const url = `${airtableConfig.apiUrl}/${airtableConfig.baseId}/${encodeURIComponent(tableName)}`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${airtableConfig.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ fields }),
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Airtable API error: ${response.status} ${errorText}`);
  }
  
  return response.json();
}

/**
 * Update an existing record
 */
export async function updateRecord<T>(
  tableName: string,
  recordId: string,
  fields: Partial<T>
): Promise<AirtableRecord<T>> {
  if (!airtableConfig.apiKey || !airtableConfig.baseId) {
    throw new Error('Airtable API key or base ID is not configured');
  }

  const url = `${airtableConfig.apiUrl}/${airtableConfig.baseId}/${encodeURIComponent(tableName)}/${recordId}`;
  
  const response = await fetch(url, {
    method: 'PATCH',
    headers: {
      Authorization: `Bearer ${airtableConfig.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ fields }),
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Airtable API error: ${response.status} ${errorText}`);
  }
  
  return response.json();
}

/**
 * Delete a record
 */
export async function deleteRecord(
  tableName: string,
  recordId: string
): Promise<{ id: string; deleted: boolean }> {
  if (!airtableConfig.apiKey || !airtableConfig.baseId) {
    throw new Error('Airtable API key or base ID is not configured');
  }

  const url = `${airtableConfig.apiUrl}/${airtableConfig.baseId}/${encodeURIComponent(tableName)}/${recordId}`;
  
  const response = await fetch(url, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${airtableConfig.apiKey}`,
    },
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Airtable API error: ${response.status} ${errorText}`);
  }
  
  return response.json();
}
