/**
 * Airtable Sync Utility
 *
 * This module provides functions to sync data between Prisma and Airtable.
 */

import { prisma } from "@/lib/prisma";
import {
  fetchRecords,
  createRecord,
  updateRecord,
  deleteRecord
} from "@/lib/airtable-sdk";
import {
  AirtableTable,
  AirtableEmployee,
  AirtableClaim,
  AirtablePolicy,
  AirtableDocument,
  AirtableComment
} from "@/types/airtable";

/**
 * Map Prisma employee to Airtable employee
 */
export function mapPrismaEmployeeToAirtable(employee: any): Partial<AirtableEmployee> {
  return {
    firstName: employee.firstName,
    lastName: employee.lastName,
    email: employee.email,
    phone: employee.phone,
    address: employee.address,
    employerName: employee.employerName,
    groupId: employee.groupId,
    memberId: employee.memberId
  };
}

/**
 * Map Airtable employee to Prisma employee
 */
export function mapAirtableEmployeeToPrisma(employee: AirtableEmployee): any {
  return {
    firstName: employee.firstName,
    lastName: employee.lastName,
    email: employee.email,
    phone: employee.phone,
    address: employee.address,
    employerName: employee.employerName,
    groupId: employee.groupId,
    memberId: employee.memberId
  };
}

/**
 * Map Prisma claim to Airtable claim
 */
export function mapPrismaClaimToAirtable(claim: any, employeeAirtableId: string): Partial<AirtableClaim> {
  return {
    employeeId: [employeeAirtableId],
    claimType: claim.claimType,
    description: claim.description,
    incidentDate: claim.incidentDate?.toISOString(),
    dateFiled: claim.dateFiled.toISOString(),
    status: claim.status
  };
}

/**
 * Map Airtable claim to Prisma claim
 */
export function mapAirtableClaimToPrisma(claim: AirtableClaim, employeeId: number): any {
  return {
    employeeId,
    claimType: claim.claimType,
    description: claim.description,
    incidentDate: claim.incidentDate ? new Date(claim.incidentDate) : null,
    dateFiled: new Date(claim.dateFiled),
    status: claim.status
  };
}

/**
 * Sync an employee from Prisma to Airtable
 */
export async function syncEmployeeToAirtable(employeeId: number, airtableId?: string): Promise<string> {
  // Get employee from Prisma
  const employee = await prisma.employee.findUnique({
    where: { id: employeeId }
  });

  if (!employee) {
    throw new Error(`Employee with ID ${employeeId} not found in Prisma`);
  }

  const airtableEmployee = mapPrismaEmployeeToAirtable(employee);

  if (airtableId) {
    // Update existing employee in Airtable
    const response = await updateRecord(
      AirtableTable.EMPLOYEES,
      airtableId,
      airtableEmployee as any
    );
    return response.id;
  } else {
    // Create new employee in Airtable
    const response = await createRecord(
      AirtableTable.EMPLOYEES,
      airtableEmployee as any
    );
    return response.id;
  }
}

/**
 * Sync a claim from Prisma to Airtable
 */
export async function syncClaimToAirtable(claimId: number, employeeAirtableId: string, airtableId?: string): Promise<string> {
  // Get claim from Prisma
  const claim = await prisma.claim.findUnique({
    where: { id: claimId }
  });

  if (!claim) {
    throw new Error(`Claim with ID ${claimId} not found in Prisma`);
  }

  const airtableClaim = mapPrismaClaimToAirtable(claim, employeeAirtableId);

  if (airtableId) {
    // Update existing claim in Airtable
    const response = await updateRecord(
      AirtableTable.CLAIMS,
      airtableId,
      airtableClaim as any
    );
    return response.id;
  } else {
    // Create new claim in Airtable
    const response = await createRecord(
      AirtableTable.CLAIMS,
      airtableClaim as any
    );
    return response.id;
  }
}

/**
 * Sync all employees from Prisma to Airtable
 */
export async function syncAllEmployeesToAirtable(): Promise<Map<number, string>> {
  // Get all employees from Prisma
  const employees = await prisma.employee.findMany();

  // Get all employees from Airtable
  const airtableResponse = await fetchRecords(AirtableTable.EMPLOYEES);

  // Create a map of email to Airtable ID
  const emailToAirtableId = new Map<string, string>();
  airtableResponse.records.forEach(record => {
    const email = record.fields.email as string;
    if (email) {
      emailToAirtableId.set(email, record.id);
    }
  });

  // Create a map of Prisma ID to Airtable ID
  const idMap = new Map<number, string>();

  // Sync each employee
  for (const employee of employees) {
    const airtableId = emailToAirtableId.get(employee.email);
    const newAirtableId = await syncEmployeeToAirtable(employee.id, airtableId);
    idMap.set(employee.id, newAirtableId);
  }

  return idMap;
}

/**
 * Sync all claims from Prisma to Airtable
 */
export async function syncAllClaimsToAirtable(employeeIdMap: Map<number, string>): Promise<void> {
  // Get all claims from Prisma
  const claims = await prisma.claim.findMany();

  // Sync each claim
  for (const claim of claims) {
    const employeeAirtableId = employeeIdMap.get(claim.employeeId);
    if (!employeeAirtableId) {
      console.warn(`No Airtable ID found for employee ${claim.employeeId}, skipping claim ${claim.id}`);
      continue;
    }

    await syncClaimToAirtable(claim.id, employeeAirtableId);
  }
}
