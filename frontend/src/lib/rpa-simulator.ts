/**
 * RPA Simulator
 *
 * This utility provides functions to simulate RPA (Robotic Process Automation) actions
 * in the browser, such as clicking buttons, filling forms, and other automated tasks.
 */

/**
 * Simulates an RPA action to create a comment from a case summary
 *
 * This function:
 * 1. Collapses the sidebar if it's expanded
 * 2. Finds the comment textarea and fills it with the summary
 * 3. Waits for the specified delay
 * 4. Clicks the "Add Comment" button
 *
 * @param summary - The summary text to add as a comment
 * @param delay - The delay in milliseconds before clicking the button (default: 2000ms)
 * @returns A promise that resolves when the action is complete
 */
export const simulateRpaAction = async (
  summary: string,
  delay: number = 2000
): Promise<void> => {
  try {
    console.log("RPA Simulation: Starting with summary:", summary);

    // Step 1: Collapse the sidebar if it's expanded
    collapseSidebar();

    // Wait a moment for the UI to stabilize after collapsing sidebar
    await new Promise(resolve => setTimeout(resolve, 300));

    // Step 2: Find and fill the comment textarea
    let success = fillCommentTextarea(summary);

    if (!success) {
      console.error("RPA Simulation: Could not find or fill the comment textarea on first attempt");

      // Wait a moment and try again - the DOM might still be updating
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log("RPA Simulation: Retrying textarea fill after waiting");
      success = fillCommentTextarea(summary);

      if (!success) {
        // Try a more direct approach - look for React fiber nodes
        console.log("RPA Simulation: Attempting to access React internals as fallback");
        tryAccessReactState(summary);
      }
    }

    // Step 3: Wait for the specified delay
    console.log(`RPA Simulation: Waiting for ${delay}ms before clicking button`);
    await new Promise(resolve => setTimeout(resolve, delay));

    // Step 4: Click the "Add Comment" button
    clickAddCommentButton();

    // Step 5: Verify if the comment was added successfully
    await verifyCommentAdded(summary);

  } catch (error) {
    console.error("RPA Simulation failed:", error);
    throw error; // Re-throw to allow the caller to handle the error
  }
};

/**
 * Verifies if a comment was successfully added
 *
 * @param commentText - The text of the comment to verify
 * @returns A promise that resolves when verification is complete
 */
const verifyCommentAdded = async (commentText: string): Promise<void> => {
  try {
    // Wait a moment for the UI to update after clicking the button
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log("RPA Simulation: Verifying if comment was added successfully");

    // Look for the comment in the comments list
    const commentElements = Array.from(document.querySelectorAll('li p.text-sm.whitespace-pre-wrap'));
    const commentFound = commentElements.some(element =>
      element.textContent?.includes(commentText.substring(0, 30))
    );

    if (commentFound) {
      console.log("RPA Simulation: Comment was successfully added and found in the UI");
    } else {
      console.log("RPA Simulation: Comment not found in UI, checking if textarea was cleared");

      // Check if the textarea was cleared (another sign of success)
      const textarea = document.querySelector('textarea[placeholder="Add a comment..."]') as HTMLTextAreaElement | null;
      if (textarea && !textarea.value.trim()) {
        console.log("RPA Simulation: Textarea was cleared, suggesting comment was added");
      } else {
        console.log("RPA Simulation: Could not verify if comment was added successfully");

        // As a last resort, try clicking the button again
        console.log("RPA Simulation: Trying to click the button one more time");
        clickAddCommentButton();
      }
    }
  } catch (error) {
    console.error("RPA Simulation: Error verifying comment:", error);
  }
};

/**
 * Advanced technique to try to access React's internal state
 * This is a last resort approach that may not work in all cases
 */
const tryAccessReactState = (text: string): void => {
  try {
    // Find the textarea element
    const textarea = document.querySelector('textarea[placeholder="Add a comment..."]') as HTMLTextAreaElement;
    if (!textarea) return;

    // Method 1: Try to access React fiber
    try {
      // @ts-ignore - Accessing React internals
      const fiberKey = Object.keys(textarea).find(key => key.startsWith('__reactFiber$'));
      if (fiberKey) {
        // @ts-ignore - Accessing React internals
        const fiber = textarea[fiberKey];
        if (fiber) {
          // Navigate the fiber tree to find the component with state
          let node = fiber;
          while (node) {
            if (node.memoizedState &&
                node.memoizedState.memoizedState &&
                typeof node.memoizedState.memoizedState === 'string') {
              // Found a string state, likely our textarea value
              console.log("RPA Simulation: Found React state node, attempting to update");

              // Try to update the state
              if (node.stateNode && typeof node.stateNode.setState === 'function') {
                node.stateNode.setState({ newComment: text });
                console.log("RPA Simulation: Updated React state directly");
              }
              break;
            }

            // Move up the fiber tree
            node = node.return;
          }
        }
      }
    } catch (fiberError) {
      console.error("RPA Simulation: Error accessing React fiber:", fiberError);
    }

    // Method 2: Try to find the React component instance
    try {
      // Look for React instance key
      // @ts-ignore - Accessing React internals
      const instanceKey = Object.keys(textarea).find(key => key.startsWith('__reactProps$'));
      if (instanceKey) {
        // @ts-ignore - Accessing React internals
        const props = textarea[instanceKey];
        if (props && typeof props.onChange === 'function') {
          // Create a synthetic event object similar to what React expects
          const syntheticEvent = {
            target: textarea,
            currentTarget: textarea,
            preventDefault: () => {},
            stopPropagation: () => {},
            bubbles: true,
            cancelable: true,
            isTrusted: true,
            type: 'change'
          };

          // Call the onChange handler directly with our text
          textarea.value = text;
          props.onChange(syntheticEvent);
          console.log("RPA Simulation: Called React onChange handler directly");
        }
      }
    } catch (propsError) {
      console.error("RPA Simulation: Error accessing React props:", propsError);
    }

    // Method 3: Try to find the parent component and update its state
    try {
      // Find the closest parent with a React instance
      let element: HTMLElement | null = textarea.parentElement;
      while (element) {
        // @ts-ignore - Accessing React internals
        const instanceKey = Object.keys(element).find(key => key.startsWith('__reactFiber$'));
        if (instanceKey) {
          // @ts-ignore - Accessing React internals
          const fiber = element[instanceKey];
          if (fiber && fiber.stateNode && typeof fiber.stateNode.setState === 'function') {
            // Try to update the state with our text
            fiber.stateNode.setState({ newComment: text });
            console.log("RPA Simulation: Updated parent component state");
            break;
          }
        }
        element = element.parentElement;
      }
    } catch (parentError) {
      console.error("RPA Simulation: Error accessing parent component:", parentError);
    }
  } catch (error) {
    console.error("RPA Simulation: Error accessing React internals:", error);
  }
};

/**
 * Enhanced function to find and update React state for a textarea
 * This combines multiple approaches to maximize the chance of success
 *
 * @param textarea - The textarea element to update
 * @param text - The text to set in the textarea
 */
const findAndUpdateReactState = (textarea: HTMLTextAreaElement, text: string): void => {
  try {
    console.log("RPA Simulation: Attempting to find and update React state");

    // Approach 1: Look for the setNewComment state setter in the component tree
    try {
      // @ts-ignore - Accessing React internals
      const fiberKey = Object.keys(textarea).find(key => key.startsWith('__reactFiber$'));
      if (fiberKey) {
        // @ts-ignore - Accessing React internals
        const fiber = textarea[fiberKey];
        if (fiber) {
          // Navigate up the fiber tree to find components with state
          let node = fiber;
          let depth = 0;
          const maxDepth = 10; // Prevent infinite loops

          while (node && depth < maxDepth) {
            // Look for state hooks in the component
            if (node.memoizedState) {
              // Check if this is a useState hook for the comment text
              let currentState = node.memoizedState;

              // Loop through the linked list of hooks
              while (currentState) {
                // If we find a string state that might be our comment text
                if (currentState.memoizedState !== undefined &&
                    (typeof currentState.memoizedState === 'string' ||
                     currentState.memoizedState === null)) {

                  console.log("RPA Simulation: Found potential state hook:", currentState.memoizedState);

                  // Check if there's a queue with a dispatch function (setState)
                  if (currentState.queue && typeof currentState.queue.dispatch === 'function') {
                    // Call the dispatch function with our text
                    currentState.queue.dispatch(text);
                    console.log("RPA Simulation: Updated state via hook dispatch");
                    return;
                  }
                }

                // Move to the next hook in the list
                currentState = currentState.next;
              }
            }

            // If the component has a setState method, try to use it
            if (node.stateNode && typeof node.stateNode.setState === 'function') {
              // Try common state property names for comment text
              const stateProps = ['newComment', 'commentText', 'text', 'value', 'comment'];

              for (const prop of stateProps) {
                try {
                  const stateUpdate = { [prop]: text };
                  node.stateNode.setState(stateUpdate);
                  console.log(`RPA Simulation: Updated component state with ${prop}:`, text);
                } catch (err) {
                  // Continue trying other properties
                }
              }
            }

            // Move up the tree
            node = node.return;
            depth++;
          }
        }
      }
    } catch (err) {
      console.error("RPA Simulation: Error in fiber tree traversal:", err);
    }

    // Approach 2: Find the closest form and try to update its state
    try {
      let form = textarea.closest('form');
      if (!form) {
        // If no direct form, look for any parent that might be a form container
        let element = textarea.parentElement;
        while (element && !form) {
          if (element.querySelector('button[type="submit"]') ||
              element.querySelector('button:contains("Add Comment")')) {
            // Cast element to any since we're treating it as a form-like container
            form = element as unknown as HTMLFormElement;
            break;
          }
          element = element.parentElement;
        }
      }

      if (form) {
        // @ts-ignore - Accessing React internals
        const formFiberKey = Object.keys(form).find(key => key.startsWith('__reactFiber$'));
        if (formFiberKey) {
          // @ts-ignore - Accessing React internals
          const formFiber = form[formFiberKey];
          if (formFiber && formFiber.stateNode && typeof formFiber.stateNode.setState === 'function') {
            // Try to update the form's state with our text
            formFiber.stateNode.setState({ newComment: text });
            console.log("RPA Simulation: Updated form component state");
          }
        }
      }
    } catch (err) {
      console.error("RPA Simulation: Error updating form state:", err);
    }

    // Approach 3: Use React DevTools global hook if available
    try {
      // @ts-ignore - Accessing React DevTools hook
      const reactDevTools = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
      if (reactDevTools && reactDevTools.renderers && reactDevTools.renderers.size > 0) {
        console.log("RPA Simulation: Found React DevTools hook, attempting to use it");

        // Get the first renderer
        const renderer = reactDevTools.renderers.get(Array.from(reactDevTools.renderers.keys())[0]);

        if (renderer && renderer.findFiberByHostInstance) {
          const fiber = renderer.findFiberByHostInstance(textarea);
          if (fiber) {
            console.log("RPA Simulation: Found fiber via DevTools hook");

            // Try to find and update state
            let node = fiber;
            while (node) {
              if (node.stateNode && typeof node.stateNode.setState === 'function') {
                node.stateNode.setState({ newComment: text });
                console.log("RPA Simulation: Updated state via DevTools hook");
                break;
              }
              node = node.return;
            }
          }
        }
      }
    } catch (err) {
      console.error("RPA Simulation: Error using React DevTools hook:", err);
    }

  } catch (error) {
    console.error("RPA Simulation: Error in findAndUpdateReactState:", error);
  }
};

/**
 * Collapses the sidebar by finding and clicking the active sidebar item
 */
const collapseSidebar = (): void => {
  try {
    // Check if the sidebar panel is expanded
    const expandedPanel = document.querySelector('.sidebar-panel[style*="width"][style*="%"]');

    if (expandedPanel) {
      // Find the active sidebar item button
      const activeSidebarItem = document.querySelector('.sidebar-icon.active');

      if (activeSidebarItem && activeSidebarItem instanceof HTMLElement) {
        // Click it to collapse the sidebar
        activeSidebarItem.click();
        console.log("RPA Simulation: Sidebar collapsed");
      } else {
        // Try an alternative approach - look for any sidebar icon
        const anySidebarIcon = document.querySelector('.sidebar-icon');
        if (anySidebarIcon && anySidebarIcon instanceof HTMLElement) {
          anySidebarIcon.click();
          console.log("RPA Simulation: Clicked a sidebar icon to collapse panel");
        } else {
          console.log("RPA Simulation: No sidebar icons found");
        }
      }
    } else {
      console.log("RPA Simulation: Sidebar panel appears to be already collapsed");
    }
  } catch (error) {
    console.error("RPA Simulation: Error collapsing sidebar:", error);
  }
};

/**
 * Finds the comment textarea and fills it with the provided text
 *
 * @param text - The text to fill in the textarea
 * @returns boolean indicating success
 */
const fillCommentTextarea = (text: string): boolean => {
  try {
    // Try multiple selectors to find the textarea
    let textarea: HTMLTextAreaElement | null = null;

    // First try: Find by placeholder
    textarea = document.querySelector('textarea[placeholder="Add a comment..."]');

    // Second try: Find any textarea in the comments section
    if (!textarea) {
      const commentsSection = Array.from(document.querySelectorAll('h3'))
        .find(h3 => h3.textContent?.includes('Comments'));

      if (commentsSection) {
        const section = commentsSection.closest('div');
        if (section) {
          textarea = section.querySelector('textarea');
        }
      }
    }

    // Third try: Find any textarea followed by an "Add Comment" button
    if (!textarea) {
      const buttons = Array.from(document.querySelectorAll('button'))
        .filter(button => button.textContent?.includes('Add Comment'));

      for (const button of buttons) {
        const parent = button.parentElement;
        if (parent) {
          const nearbyTextarea = parent.querySelector('textarea') as HTMLTextAreaElement | null;
          if (nearbyTextarea) {
            textarea = nearbyTextarea;
            break;
          }
        }
      }
    }

    // If we found a textarea, fill it
    if (textarea && textarea instanceof HTMLTextAreaElement) {
      // Check if the textarea is disabled and try to enable it
      if (textarea.disabled) {
        console.log("RPA Simulation: Textarea is disabled, trying to enable it");
        textarea.disabled = false;
      }

      // Focus the textarea first (important for React to track events)
      textarea.focus();

      // IMPROVED APPROACH: Find the React component's onChange handler directly
      // This is more reliable than just setting the value property
      let reactHandlerFound = false;

      // Look for React props on the textarea
      // @ts-ignore - Accessing React internals
      const propsKey = Object.keys(textarea).find(key => key.startsWith('__reactProps$'));
      if (propsKey) {
        // @ts-ignore - Accessing React internals
        const props = textarea[propsKey];
        if (props && typeof props.onChange === 'function') {
          console.log("RPA Simulation: Found React onChange handler directly");

          // Create a synthetic event object similar to what React expects
          const syntheticEvent = {
            target: { value: text },
            currentTarget: { value: text },
            preventDefault: () => {},
            stopPropagation: () => {},
            bubbles: true,
            cancelable: true,
            isTrusted: true,
            type: 'change'
          };

          // Call the onChange handler directly with our text
          props.onChange(syntheticEvent);
          reactHandlerFound = true;
          console.log("RPA Simulation: Called React onChange handler directly");
        }
      }

      // If we couldn't find the React handler, fall back to DOM manipulation
      if (!reactHandlerFound) {
        console.log("RPA Simulation: Falling back to DOM manipulation");

        // Set the value using direct property access
        textarea.value = text;

        // Create and dispatch events that React is listening for

        // 1. Input event - this is what React forms typically listen to for immediate updates
        const inputEvent = new Event('input', { bubbles: true, cancelable: true });
        Object.defineProperty(inputEvent, 'target', { value: { value: text } });
        const inputHandled = textarea.dispatchEvent(inputEvent);
        console.log("RPA Simulation: Input event dispatched, handled:", inputHandled);

        // 2. Change event - React often uses this for final value confirmation
        const changeEvent = new Event('change', { bubbles: true, cancelable: true });
        Object.defineProperty(changeEvent, 'target', { value: { value: text } });
        const changeHandled = textarea.dispatchEvent(changeEvent);
        console.log("RPA Simulation: Change event dispatched, handled:", changeHandled);

        // 3. Keydown, keypress, and keyup events to simulate typing
        // This can help trigger React's event handlers in some implementations
        ['keydown', 'keypress', 'keyup'].forEach(eventType => {
          const keyEvent = new KeyboardEvent(eventType, { bubbles: true, cancelable: true });
          textarea.dispatchEvent(keyEvent);
        });

        // 4. Blur and focus events to ensure React captures the change
        textarea.blur();
        textarea.focus();

        // Double-check the value was set
        if (textarea.value !== text) {
          console.log("RPA Simulation: Value not set correctly, trying alternative method");

          // Try using the Selection API to insert text (works in some cases where direct assignment doesn't)
          textarea.select();
          document.execCommand('insertText', false, text);
        }
      }

      // Try to find the React component instance and update its state directly
      findAndUpdateReactState(textarea, text);

      console.log("RPA Simulation: Comment textarea filled with:", text);
      return true;
    } else {
      console.log("RPA Simulation: Comment textarea not found after multiple attempts");
      return false;
    }
  } catch (error) {
    console.error("RPA Simulation: Error filling comment textarea:", error);
    return false;
  }
};

/**
 * Finds and clicks the "Add Comment" button
 */
const clickAddCommentButton = (): void => {
  try {
    console.log("RPA Simulation: Looking for Add Comment button");

    // Find the button with text "Add Comment"
    const buttons = Array.from(document.querySelectorAll('button'));
    let addCommentButton = buttons.find(button =>
      button.textContent?.includes('Add Comment') &&
      !button.disabled
    );

    // If we didn't find an enabled button, try to find one near the textarea
    if (!addCommentButton) {
      console.log("RPA Simulation: No enabled Add Comment button found, looking near textarea");

      // Find the textarea first
      const textarea = document.querySelector('textarea[placeholder="Add a comment..."]');

      if (textarea) {
        // Look for a button near the textarea - search in parent containers
        let parent = textarea.parentElement;
        const maxLevelsUp = 5; // Don't go too far up the DOM tree
        let levelsUp = 0;

        while (parent && !addCommentButton && levelsUp < maxLevelsUp) {
          const nearbyButtons = Array.from(parent.querySelectorAll('button'));
          addCommentButton = nearbyButtons.find(button =>
            button.textContent?.includes('Add Comment') ||
            button.textContent?.includes('Comment') ||
            button.textContent?.includes('Add')
          );

          if (!addCommentButton) {
            parent = parent.parentElement;
            levelsUp++;
          }
        }

        // If we still didn't find it, try a more aggressive approach
        if (!addCommentButton) {
          console.log("RPA Simulation: Still no button found, trying more aggressive search");

          // Look for any button that might be related to comments
          const commentButtons = buttons.filter(button => {
            const text = button.textContent?.toLowerCase() || '';
            return text.includes('comment') || text.includes('add');
          });

          if (commentButtons.length > 0) {
            addCommentButton = commentButtons[0];
            console.log("RPA Simulation: Found potential comment button:", addCommentButton.textContent);
          }
        }
      }
    }

    // If we found a button, try to click it
    if (addCommentButton && addCommentButton instanceof HTMLButtonElement) {
      console.log("RPA Simulation: Found Add Comment button:", addCommentButton.textContent);

      // Check if it's disabled and try to enable it
      if (addCommentButton.disabled) {
        console.log("RPA Simulation: Add Comment button is disabled, trying to enable it");

        // Try to enable the button by setting disabled to false and removing the attribute
        addCommentButton.disabled = false;
        addCommentButton.removeAttribute('disabled');

        // Try to update the React props to enable the button
        try {
          // @ts-ignore - Accessing React internals
          const propsKey = Object.keys(addCommentButton).find(key => key.startsWith('__reactProps$'));
          if (propsKey) {
            // @ts-ignore - Accessing React internals
            const props = addCommentButton[propsKey];
            if (props) {
              props.disabled = false;
              console.log("RPA Simulation: Updated React props to enable button");
            }
          }
        } catch (propsError) {
          console.error("RPA Simulation: Error updating button props:", propsError);
        }

        // Try to find and update the React component state that controls the button
        try {
          // @ts-ignore - Accessing React internals
          const fiberKey = Object.keys(addCommentButton).find(key => key.startsWith('__reactFiber$'));
          if (fiberKey) {
            // @ts-ignore - Accessing React internals
            const fiber = addCommentButton[fiberKey];
            if (fiber) {
              // Navigate up the fiber tree to find components with state
              let node = fiber;
              while (node) {
                if (node.stateNode && typeof node.stateNode.setState === 'function') {
                  // Try common state properties that might control button disabled state
                  const stateUpdates = [
                    { isAddingComment: false },
                    { isSubmitting: false },
                    { isLoading: false },
                    { disabled: false },
                    { isDisabled: false }
                  ];

                  for (const update of stateUpdates) {
                    try {
                      node.stateNode.setState(update);
                      console.log("RPA Simulation: Updated component state to enable button:", update);
                    } catch (err) {
                      // Continue trying other properties
                    }
                  }
                }
                node = node.return;
              }
            }
          }
        } catch (fiberError) {
          console.error("RPA Simulation: Error updating button component state:", fiberError);
        }

        // If the button is still disabled, try to make the textarea have valid content
        if (addCommentButton.disabled) {
          console.log("RPA Simulation: Button still disabled, trying to ensure textarea has valid content");

          // Look for the textarea again
          const textarea = document.querySelector('textarea[placeholder="Add a comment..."]');
          if (textarea && textarea instanceof HTMLTextAreaElement) {
            // Make sure the textarea has content and trigger all possible events
            if (!textarea.value.trim()) {
              // If empty, add some content
              textarea.value = "Automated comment from RPA system";
            }

            // Focus and trigger events
            textarea.focus();

            // Dispatch all possible events that might trigger validation
            ['input', 'change', 'keyup', 'keydown', 'keypress', 'blur', 'focus'].forEach(eventType => {
              const event = new Event(eventType, { bubbles: true });
              textarea.dispatchEvent(event);
            });

            // Try to directly call the handleAddComment function if we can find it
            try {
              // @ts-ignore - Accessing window object
              if (typeof window.handleAddComment === 'function') {
                // @ts-ignore - Calling global function
                window.handleAddComment();
                console.log("RPA Simulation: Called global handleAddComment function");
                return; // If we successfully called the handler, we're done
              }
            } catch (globalError) {
              console.error("RPA Simulation: Error calling global handler:", globalError);
            }
          }
        }
      }

      // Try multiple approaches to click the button
      console.log("RPA Simulation: Attempting to click the Add Comment button");

      // Approach 1: Standard click
      try {
        addCommentButton.click();
        console.log("RPA Simulation: Add Comment button clicked successfully");
        return; // If click succeeded, we're done
      } catch (clickError) {
        console.error("RPA Simulation: Error with standard click:", clickError);
      }

      // Approach 2: MouseEvent
      try {
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        addCommentButton.dispatchEvent(clickEvent);
        console.log("RPA Simulation: Dispatched MouseEvent click");
        return; // If this worked, we're done
      } catch (mouseEventError) {
        console.error("RPA Simulation: Error dispatching MouseEvent:", mouseEventError);
      }

      // Approach 3: Try to find and call the onClick handler directly
      try {
        // @ts-ignore - Accessing React internals
        const propsKey = Object.keys(addCommentButton).find(key => key.startsWith('__reactProps$'));
        if (propsKey) {
          // @ts-ignore - Accessing React internals
          const props = addCommentButton[propsKey];
          if (props && typeof props.onClick === 'function') {
            // Create a synthetic event object
            const syntheticEvent = {
              target: addCommentButton,
              currentTarget: addCommentButton,
              preventDefault: () => {},
              stopPropagation: () => {},
              bubbles: true,
              cancelable: true,
              isTrusted: true,
              type: 'click'
            };

            // Call the onClick handler directly
            props.onClick(syntheticEvent);
            console.log("RPA Simulation: Called onClick handler directly");
            return; // If this worked, we're done
          }
        }
      } catch (onClickError) {
        console.error("RPA Simulation: Error calling onClick handler:", onClickError);
      }

    } else {
      console.log("RPA Simulation: Add Comment button not found, trying fallbacks");

      // Fallback 1: Try to submit the form if there is one
      const form = document.querySelector('form');
      if (form) {
        console.log("RPA Simulation: Found a form, trying to submit it");
        try {
          form.dispatchEvent(new Event('submit', { bubbles: true }));
          console.log("RPA Simulation: Form submit event dispatched");
          return; // If this worked, we're done
        } catch (formError) {
          console.error("RPA Simulation: Error submitting form:", formError);
        }
      }

      // Fallback 2: Try to find any button that might be the submit button
      const allButtons = Array.from(document.querySelectorAll('button'));
      const possibleSubmitButtons = allButtons.filter(button => {
        const text = button.textContent?.toLowerCase() || '';
        return (text.includes('submit') || text.includes('save') || text.includes('add') ||
                text.includes('comment')) && !button.disabled;
      });

      if (possibleSubmitButtons.length > 0) {
        console.log("RPA Simulation: Trying to click a possible submit button as last resort:",
                    possibleSubmitButtons[0].textContent);
        try {
          possibleSubmitButtons[0].click();
          console.log("RPA Simulation: Clicked potential submit button");
        } catch (buttonError) {
          console.error("RPA Simulation: Error clicking potential submit button:", buttonError);
        }
      } else {
        console.log("RPA Simulation: No suitable buttons found for adding a comment");
      }

      // Fallback 3: Try to directly call the handleAddComment function from the claim-detail component
      try {
        // Look for any component that might have a handleAddComment method
        const components = document.querySelectorAll('*');
        for (let i = 0; i < components.length; i++) {
          const component = components[i];
          // @ts-ignore - Accessing React internals
          const fiberKey = Object.keys(component).find(key => key.startsWith('__reactFiber$'));
          if (fiberKey) {
            // @ts-ignore - Accessing React internals
            const fiber = component[fiberKey];
            if (fiber && fiber.stateNode) {
              // Check if this component has a handleAddComment method
              if (typeof fiber.stateNode.handleAddComment === 'function') {
                fiber.stateNode.handleAddComment();
                console.log("RPA Simulation: Called component's handleAddComment method");
                return; // If this worked, we're done
              }
            }
          }
        }
      } catch (componentError) {
        console.error("RPA Simulation: Error finding component with handleAddComment:", componentError);
      }
    }
  } catch (error) {
    console.error("RPA Simulation: Error clicking Add Comment button:", error);
  }
};
