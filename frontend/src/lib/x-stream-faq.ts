/**
 * X-Stream FAQ API Client
 *
 * This module provides functions to interact with the X-Stream FAQ API.
 */

import { getAuthToken } from "./x-stream-auth";

// Configuration for X-Stream FAQ API
interface XStreamFaqConfig {
  apiUrl: string;
  llmInferenceUrl: string;
  kbId: string;
}

// FAQ API response types
export interface FaqItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface FaqSearchResponse {
  items: FaqItem[];
  total: number;
  page: number;
  pageSize: number;
}

// LLM Inference API response types
export interface Source {
  chunkId: string;
  parentId: string;
  importance: number;
  content: string;
  parentDocName: string;
  title: string;
}

export interface ContextSearchDocument {
  documentId: string;
  name: string;
  title: string;
}

export interface Factuality {
  isFactual: boolean;
  score: string;
}

export interface LlmInferenceResponse {
  response: string;
  factuality: Factuality;
  sources: Source[];
  id: string;
  contextSearchDocuments: ContextSearchDocument[];
}

// API configuration
export const xStreamFaqConfig: XStreamFaqConfig = {
  apiUrl: process.env.XSTREAM_FAQ_API_URL || "https://api.uniphore.com/faq",
  llmInferenceUrl: process.env.XSTREAM_LLM_INFERENCE_URL || "https://api.us2e2a.cloud.uniphore.com/llm-inference/v1/answer-question-with-rag",
  kbId: process.env.XSTREAM_KB_ID || "85420b1b-7d15-4e3f-b737-935762dc5405",
};

/**
 * Base function to make authenticated requests to the X-Stream FAQ API
 * @param endpoint API endpoint path
 * @param options Fetch options
 * @returns Promise with the API response
 */
async function makeAuthenticatedRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // Get a valid auth token
    const token = await getAuthToken();

    // Prepare the full URL
    const url = `${xStreamFaqConfig.apiUrl}${endpoint}`;

    // Set up headers with the auth token
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X-Stream FAQ API error: ${response.status} ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error making authenticated request to X-Stream FAQ API:", error);
    throw error;
  }
}

/**
 * Search for FAQs
 * @param query Search query
 * @param options Search options
 * @returns Promise with the search response
 */
export async function searchFaqs(
  query: string,
  options: {
    category?: string;
    tags?: string[];
    page?: number;
    pageSize?: number;
  } = {}
): Promise<FaqSearchResponse> {
  const { category, tags, page = 1, pageSize = 10 } = options;

  // Build query parameters
  const params = new URLSearchParams({
    q: query,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  if (category) {
    params.append("category", category);
  }

  if (tags && tags.length > 0) {
    tags.forEach(tag => params.append("tags", tag));
  }

  return makeAuthenticatedRequest<FaqSearchResponse>(`/search?${params.toString()}`);
}

/**
 * Get a FAQ by ID
 * @param id FAQ ID
 * @returns Promise with the FAQ item
 */
export async function getFaqById(id: string): Promise<FaqItem> {
  return makeAuthenticatedRequest<FaqItem>(`/${id}`);
}

/**
 * Get all FAQ categories
 * @returns Promise with the list of categories
 */
export async function getFaqCategories(): Promise<string[]> {
  return makeAuthenticatedRequest<string[]>("/categories");
}

/**
 * Get all FAQ tags
 * @returns Promise with the list of tags
 */
export async function getFaqTags(): Promise<string[]> {
  return makeAuthenticatedRequest<string[]>("/tags");
}

/**
 * Answer a question using the LLM inference API with RAG
 * @param query The question to answer
 * @param kbId Optional knowledge base ID (defaults to the configured kbId)
 * @returns Promise with the LLM inference response
 */
export async function answerQuestion(
  query: string,
  kbId: string = xStreamFaqConfig.kbId
): Promise<LlmInferenceResponse> {
  try {
    // Get a valid auth token
    const token = await getAuthToken();

    // Prepare the request payload
    const payload = {
      query,
      kb_id: kbId,
    };

    // Make the request
    const response = await fetch(xStreamFaqConfig.llmInferenceUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`LLM Inference API error: ${response.status} ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error answering question with LLM Inference API:", error);
    throw error;
  }
}
