/**
 * X-Stream Authentication Service
 * 
 * This module provides functions to handle authentication with Auth0 for X-Stream API.
 * It manages token generation, caching, and refreshing.
 */

// Configuration for X-Stream Auth
interface XStreamAuthConfig {
  clientId: string | undefined;
  clientSecret: string | undefined;
  tokenUrl: string;
  audience: string;
  grantType: string;
}

// Auth token response type
interface AuthTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope?: string;
}

// Token cache with expiration
interface TokenCache {
  token: string;
  expiresAt: number; // Timestamp when the token expires
}

// Auth configuration
export const xStreamAuthConfig: XStreamAuthConfig = {
  clientId: process.env.XSTREAM_CLIENT_ID,
  clientSecret: process.env.XSTREAM_CLIENT_SECRET,
  tokenUrl: "https://uniphore.us.auth0.com/oauth/token",
  audience: "api.uniphore.com",
  grantType: "client_credentials",
};

// Token cache (in-memory)
let tokenCache: TokenCache | null = null;

/**
 * Validates the auth configuration
 * @throws Error if configuration is invalid
 */
function validateConfig(): void {
  if (!xStreamAuthConfig.clientId) {
    throw new Error("X-Stream client ID is not configured. Set XSTREAM_CLIENT_ID environment variable.");
  }

  if (!xStreamAuthConfig.clientSecret) {
    throw new Error("X-Stream client secret is not configured. Set XSTREAM_CLIENT_SECRET environment variable.");
  }
}

/**
 * Generates a new auth token
 * @returns Promise with the auth token response
 */
async function generateToken(): Promise<AuthTokenResponse> {
  validateConfig();

  const data = {
    client_id: xStreamAuthConfig.clientId,
    client_secret: xStreamAuthConfig.clientSecret,
    grant_type: xStreamAuthConfig.grantType,
    audience: xStreamAuthConfig.audience,
  };

  try {
    const response = await fetch(xStreamAuthConfig.tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Auth0 API error: ${response.status} ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error generating auth token:", error);
    throw new Error("Failed to generate auth token");
  }
}

/**
 * Gets a valid auth token, either from cache or by generating a new one
 * @returns Promise with the auth token
 */
export async function getAuthToken(): Promise<string> {
  const now = Date.now();

  // If we have a cached token that's still valid, return it
  if (tokenCache && tokenCache.expiresAt > now) {
    return tokenCache.token;
  }

  // Otherwise, generate a new token
  try {
    const tokenResponse = await generateToken();
    
    // Calculate expiration time (subtract 60 seconds as a buffer)
    const expiresAt = now + (tokenResponse.expires_in - 60) * 1000;
    
    // Cache the token
    tokenCache = {
      token: tokenResponse.access_token,
      expiresAt,
    };
    
    return tokenResponse.access_token;
  } catch (error) {
    console.error("Error getting auth token:", error);
    throw error;
  }
}

/**
 * Clears the token cache, forcing a new token to be generated on next request
 */
export function clearTokenCache(): void {
  tokenCache = null;
}
