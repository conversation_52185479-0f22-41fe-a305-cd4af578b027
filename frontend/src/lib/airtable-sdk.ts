/**
 * Airtable SDK service
 *
 * This module provides a wrapper around the official Airtable SDK.
 */

import Airtable, { FieldSet } from "airtable";

/**
 * Configuration for Airtable SDK
 */
export const airtableConfig = {
  apiKey: process.env.AIRTABLE_API_KEY,
  baseId: process.env.AIRTABLE_BASE_ID,
  // apiUrl: "https://api.airtable.com/v0",
};

/**
 * Initialize Airtable SDK
 */
export function initAirtableSDK() {
  if (!airtableConfig.apiKey) {
    throw new Error("Airtable API key is not configured");
  }

  // Configure Airtable globally
  Airtable.configure({
    apiKey: airtableConfig.apiKey,
  });

  return Airtable;
}

/**
 * Get Airtable base
 */
export function getBase() {
  if (!airtableConfig.baseId) {
    throw new Error("Airtable base ID is not configured");
  }

  const airtable = initAirtableSDK();
  return airtable.base(airtableConfig.baseId);
}

/**
 * Generic type for Airtable record
 */
export interface AirtableRecord<T extends FieldSet> {
  id: string;
  fields: T;
  createdTime: string;
}

/**
 * Generic type for Airtable response
 */
export interface AirtableResponse<T extends FieldSet> {
  records: AirtableRecord<T>[];
  offset?: string;
}

/**
 * Options for fetching records
 */
export interface FetchOptions {
  maxRecords?: number;
  view?: string;
  filterByFormula?: string;
  sort?: Array<{ field: string; direction: "asc" | "desc" }>;
  offset?: number;
}

/**
 * Fetch records from a table
 */
export async function fetchRecords<T extends FieldSet>(
  tableName: string,
  options: FetchOptions = {},
): Promise<AirtableResponse<T>> {
  const base = getBase();
  const table = base(tableName);

  const records = await table.select(options).all();

  return {
    records: records.map((record) => ({
      id: record.id,
      fields: record.fields as T,
      createdTime: record._rawJson.createdTime,
    })),
  };
}

/**
 * Fetch a single record by ID
 */
export async function fetchRecord<T extends FieldSet>(
  tableName: string,
  recordId: string,
): Promise<AirtableRecord<T>> {
  const base = getBase();
  const table = base(tableName);

  const record = await table.find(recordId);

  return {
    id: record.id,
    fields: record.fields as T,
    createdTime: record._rawJson.createdTime,
  };
}

/**
 * Create a new record
 */
export async function createRecord<T extends FieldSet>(
  tableName: string,
  fields: Partial<T>,
): Promise<AirtableRecord<T>> {
  const base = getBase();
  const table = base(tableName);

  try {
    const record = await table.create(fields as Partial<FieldSet>);
    return {
      id: record.id,
      fields: record.fields as T,
      createdTime: record._rawJson.createdTime,
    };
  } catch (error) {
    console.error("Error creating record:", error);
    throw error;
  }
}

/**
 * Update a record
 */
export async function updateRecord<T extends FieldSet>(
  tableName: string,
  recordId: string,
  fields: Partial<T>,
): Promise<AirtableRecord<T>> {
  const base = getBase();
  const table = base(tableName);

  const record = await table.update(recordId, fields as Partial<FieldSet>);

  return {
    id: record.id,
    fields: record.fields as T,
    createdTime: record._rawJson.createdTime,
  };
}

/**
 * Delete a record
 */
export async function deleteRecord(
  tableName: string,
  recordId: string,
): Promise<{ id: string; deleted: boolean }> {
  const base = getBase();
  const table = base(tableName);

  const record = await table.destroy(recordId);

  return {
    id: record.id,
    deleted: true,
  };
}
