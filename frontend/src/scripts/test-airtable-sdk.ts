/**
 * Test script for Airtable SDK
 * 
 * This script tests the Airtable SDK implementation.
 * 
 * Usage:
 * npx ts-node --esm src/scripts/test-airtable-sdk.ts
 */

import { 
  initAirtableSDK, 
  getBase, 
  fetchRecords, 
  fetchRecord, 
  createRecord, 
  updateRecord, 
  deleteRecord 
} from '../lib/airtable-sdk';
import { AirtableTable } from '../types/airtable';

// Load environment variables
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

async function testAirtableSDK() {
  try {
    console.log('Testing Airtable SDK...');
    
    // Initialize SDK
    console.log('Initializing SDK...');
    const airtable = initAirtableSDK();
    console.log('SDK initialized successfully');
    
    // Get base
    console.log('Getting base...');
    const base = getBase();
    console.log('Base retrieved successfully');
    
    // Fetch records
    console.log('Fetching records...');
    const employees = await fetchRecords(AirtableTable.EMPLOYEES, { maxRecords: 10 });
    console.log(`Retrieved ${employees.records.length} employees`);
    
    if (employees.records.length > 0) {
      const employeeId = employees.records[0].id;
      
      // Fetch single record
      console.log(`Fetching employee with ID ${employeeId}...`);
      const employee = await fetchRecord(AirtableTable.EMPLOYEES, employeeId);
      console.log('Employee retrieved successfully:', employee.fields);
    }
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Error testing Airtable SDK:', error);
  }
}

// Run the test
testAirtableSDK();
