/**
 * Test script for X-Stream FAQ API
 *
 * This script tests the X-Stream FAQ API implementation.
 *
 * Usage:
 * npx ts-node --esm src/scripts/test-x-stream-faq.ts
 */

import { getAuthToken, clearTokenCache } from "../lib/x-stream-auth";
import {
  searchFaqs,
  getFaqById,
  getFaqCategories,
  getFaqTags,
  answerQuestion,
} from "../lib/x-stream-faq";

// Load environment variables
import * as dotenv from "dotenv";
dotenv.config({ path: '.env.local' });

async function testXStreamFaqApi() {
  console.log("Testing X-Stream FAQ API...");

  try {
    // Test authentication
    console.log("\n--- Testing authentication ---");
    console.time("Authentication request");
    const token = await getAuthToken();
    console.timeEnd("Authentication request");
    console.log(
      "Auth token generated successfully:",
      token.substring(0, 10) + "...",
    );

    // Test token caching
    console.log("\n--- Testing token caching ---");
    console.time("First token request");
    await getAuthToken();
    console.timeEnd("First token request");

    console.time("Second token request (should be faster due to caching)");
    await getAuthToken();
    console.timeEnd("Second token request (should be faster due to caching)");

    // Clear token cache and test again
    console.log("\n--- Testing token refresh ---");
    clearTokenCache();
    console.time("Token request after cache clear");
    await getAuthToken();
    console.timeEnd("Token request after cache clear");

    // Test LLM inference API
    console.log("\n--- Testing LLM inference API ---");
    console.log("Asking question: 'what is the name on the Cardiac Enzyme Test Report'");
    const answer = await answerQuestion("what is the name on the Cardiac Enzyme Test Report");

    console.log("Answer:", answer.response);
    console.log("Factuality:", answer.factuality);
    console.log("Sources:", answer.sources.length);
    console.log("First source document:", answer.sources[0]?.parentDocName);
    console.log("Context search documents:", answer.contextSearchDocuments.map(doc => doc.name).join(", "));

    console.log("\nAll tests completed successfully!");
  } catch (error) {
    console.error("Error testing X-Stream FAQ API:", error);
  }
}

// Run the tests
testXStreamFaqApi();
