# Airtable SDK Integration

This project now uses the official Airtable SDK for TypeScript/JavaScript to interact with Airtable.

## Configuration

The Airtable SDK requires the following environment variables:

- `AIRTABLE_API_KEY`: Your Airtable API key
- `AIRTABLE_BASE_ID`: Your Airtable base ID

These should be set in your `.env.local` file or in your environment.

## Usage

The SDK is implemented in `src/lib/airtable-sdk.ts` and provides the following functions:

### Basic Functions

- `initAirtableSDK()`: Initialize the Airtable SDK
- `getBase()`: Get the Airtable base

### Data Operations

- `fetchRecords<T>(tableName, options)`: Fetch records from a table
- `fetchRecord<T>(tableName, recordId)`: Fetch a single record by ID
- `createRecord<T>(tableName, fields)`: Create a new record
- `updateRecord<T>(tableName, recordId, fields)`: Update a record
- `deleteRecord(tableName, recordId)`: Delete a record

## Example

```typescript
import { fetchRecords, createRecord } from '@/lib/airtable-sdk';
import { AirtableTable, AirtableClaim } from '@/types/airtable';

// Fetch records
const response = await fetchRecords<AirtableClaim>(
  AirtableTable.CLAIMS,
  { maxRecords: 10 }
);

// Create a record
const claim: Partial<AirtableClaim> = {
  employeeId: ['rec123'],
  claimType: 'Critical Illness',
  description: 'Test claim',
  dateFiled: new Date().toISOString(),
  status: 'Pending'
};

const newRecord = await createRecord<AirtableClaim>(
  AirtableTable.CLAIMS,
  claim
);
```

## Benefits of Using the SDK

1. **Type Safety**: The SDK provides better TypeScript support
2. **Error Handling**: Improved error handling and consistency
3. **Performance**: The SDK is optimized for performance
4. **Maintainability**: Easier to maintain and update
5. **Documentation**: Better documentation and community support

## Troubleshooting

If you encounter issues with the Airtable SDK:

1. Check that your environment variables are correctly set
2. Ensure you have the correct permissions for the Airtable base
3. Check the Airtable API documentation for any changes or limitations
4. Look for error messages in the console logs

## Resources

- [Airtable API Documentation](https://airtable.com/developers/web/api/introduction)
- [Airtable SDK on GitHub](https://github.com/airtable/airtable.js)
- [Airtable SDK on npm](https://www.npmjs.com/package/airtable)
