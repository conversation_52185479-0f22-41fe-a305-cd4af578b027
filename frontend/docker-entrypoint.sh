#!/bin/sh
set -e

# Initialize database directory structure
echo "Initializing database directory structure..."
/usr/local/bin/init-db-directory.sh

# Run database migrations
echo "Running database migrations..."
# Check if the database file exists and has content before running migrations
if [ -f /app/prisma/db/dev.db ] && [ -s /app/prisma/db/dev.db ]; then
  echo "Database file exists and has content. Running migrations carefully..."
  # Create a backup before migrations just in case
  cp /app/prisma/db/dev.db /app/prisma/db/dev.db.backup
  echo "Database backup created at /app/prisma/db/dev.db.backup"
fi

# Run the migrations with error handling
if ! npx prisma migrate deploy --schema=./prisma/schema.prisma; then
  echo "ERROR: Database migration failed!"
  if [ -f /app/prisma/db/dev.db.backup ]; then
    echo "Restoring database from backup..."
    cp /app/prisma/db/dev.db.backup /app/prisma/db/dev.db
    echo "Database restored from backup."
  fi
  # Continue anyway to allow the application to start
  echo "Continuing despite migration failure..."
else
  echo "Database migrations completed successfully."
fi

# Start the application
echo "Starting the application..."
exec "$@"
