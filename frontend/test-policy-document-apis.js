// Test script for policy document APIs
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const POLICY_ID = 1; // Replace with an actual policy ID from your database

// Test URL-based document attachment
async function testUrlBasedAttachment() {
  console.log('\n--- Testing URL-based document attachment ---');
  
  try {
    const response = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents/from-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        fileName: 'sample-policy-document.pdf'
      }),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ URL-based attachment successful');
      console.log('Document ID:', data.id);
      console.log('File name:', data.fileName);
    } else {
      console.log('❌ URL-based attachment failed');
      console.log('Error:', data.error);
    }
  } catch (error) {
    console.log('❌ URL-based attachment failed with exception');
    console.log('Error:', error.message);
  }
}

// Test file upload document attachment
async function testFileUploadAttachment() {
  console.log('\n--- Testing file upload document attachment ---');
  
  try {
    // Create a test file if it doesn't exist
    const testFilePath = path.join(__dirname, 'test-document.txt');
    if (!fs.existsSync(testFilePath)) {
      fs.writeFileSync(testFilePath, 'This is a test document for policy attachment.');
    }
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('customFileName', 'custom-policy-document.txt');
    
    const response = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents/upload`, {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ File upload attachment successful');
      console.log('Document ID:', data.id);
      console.log('File name:', data.fileName);
    } else {
      console.log('❌ File upload attachment failed');
      console.log('Error:', data.error);
    }
  } catch (error) {
    console.log('❌ File upload attachment failed with exception');
    console.log('Error:', error.message);
  }
}

// Test getting policy documents
async function testGetPolicyDocuments() {
  console.log('\n--- Testing get policy documents ---');
  
  try {
    const response = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Get policy documents successful');
      console.log('Number of documents:', data.length);
      if (data.length > 0) {
        console.log('First document:', data[0]);
      }
    } else {
      console.log('❌ Get policy documents failed');
      console.log('Error:', data.error);
    }
  } catch (error) {
    console.log('❌ Get policy documents failed with exception');
    console.log('Error:', error.message);
  }
}

// Run the tests
async function runTests() {
  console.log('Starting policy document API tests...');
  
  await testUrlBasedAttachment();
  await testFileUploadAttachment();
  await testGetPolicyDocuments();
  
  console.log('\nTests completed.');
}

runTests().catch(console.error);
