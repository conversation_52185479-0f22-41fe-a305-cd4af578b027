import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      typography: {
        DEFAULT: {
          css: {
            maxWidth: '100%',
            color: 'var(--sidebar-panel-text)',
            a: {
              color: 'var(--sidebar-panel-accent)',
              '&:hover': {
                color: 'var(--sidebar-panel-accent-hover)',
              },
            },
            h1: {
              color: 'var(--sidebar-panel-text)',
            },
            h2: {
              color: 'var(--sidebar-panel-text)',
            },
            h3: {
              color: 'var(--sidebar-panel-text)',
            },
            h4: {
              color: 'var(--sidebar-panel-text)',
            },
            strong: {
              color: 'var(--sidebar-panel-text)',
            },
            code: {
              color: 'var(--sidebar-panel-text)',
            },
            blockquote: {
              color: 'var(--sidebar-panel-text)',
              borderLeftColor: 'var(--sidebar-panel-accent)',
            },
          },
        },
      },
    },
  },
  plugins: [require("@tailwindcss/typography")],
};

export default config;
