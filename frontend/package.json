{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:project": "eslint 'src/**/*.{ts,tsx}'", "seed:policies": "node --loader ts-node/esm prisma/seed-policies.ts", "test:airtable": "node --loader ts-node/esm src/scripts/test-airtable-sdk.ts", "debug:airtable": "npx ts-node src/tests/airtable-debug.ts", "debug:airtable:simple": "npx ts-node src/tests/simple-airtable-debug.ts", "debug:airtable:js": "node src/tests/simple-airtable-debug.cjs", "debug:airtable:esm": "node src/tests/simple-airtable-debug.mjs"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.6.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "airtable": "^0.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "multer": "^1.4.5-lts.2", "next": "15.3.1", "next-swagger-doc": "^0.4.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "swagger-ui-react": "^5.21.0", "tailwind-merge": "^3.2.0", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.6.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.8", "typescript": "^5"}}