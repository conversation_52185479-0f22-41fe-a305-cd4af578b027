const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs');

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding policy data...');

  // Get all employees
  const employees = await prisma.employee.findMany();
  
  if (employees.length === 0) {
    console.log('No employees found. Please run the main seed script first.');
    return;
  }

  // Create sample policies for each employee
  for (const employee of employees) {
    // Check if employee already has a policy
    const existingPolicy = await prisma.policy.findUnique({
      where: { employeeId: employee.id }
    });

    if (existingPolicy) {
      console.log(`Employee ${employee.firstName} ${employee.lastName} already has a policy.`);
      continue;
    }

    // Create a policy for this employee
    const policy = await prisma.policy.create({
      data: {
        employeeId: employee.id,
        policyOwner: `${employee.firstName} ${employee.lastName}`,
        insured: `${employee.firstName} ${employee.lastName}`,
        spouse: Math.random() > 0.5 ? '<PERSON>' : null,
        group: employee.groupId || 'GRP12345',
        policyNumber: `POL-${Math.floor(100000 + Math.random() * 900000)}`,
        originalEffectiveDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(1 + Math.random() * 28)),
        scheduledEffectiveDate: new Date(2024, Math.floor(Math.random() * 12), Math.floor(1 + Math.random() * 28)),
        issuedAge: Math.floor(25 + Math.random() * 40),
        insuredCoverage: Math.floor(50000 + Math.random() * 950000),
        spouseCoverage: Math.random() > 0.5 ? Math.floor(25000 + Math.random() * 475000) : null,
      }
    });

    console.log(`Created policy for ${employee.firstName} ${employee.lastName}`);

    // Create sample policy documents
    const documentsDir = path.join(__dirname, '../public/sample-documents');
    
    // Check if the directory exists
    if (fs.existsSync(documentsDir)) {
      const sampleFiles = fs.readdirSync(documentsDir);
      
      // Add 1-2 random documents
      const numDocs = Math.floor(1 + Math.random() * 2);
      for (let i = 0; i < numDocs && i < sampleFiles.length; i++) {
        const randomIndex = Math.floor(Math.random() * sampleFiles.length);
        const fileName = sampleFiles[randomIndex];
        
        await prisma.policyDocument.create({
          data: {
            policyId: policy.id,
            fileName: `Policy Document - ${fileName}`,
            filePath: path.join(documentsDir, fileName),
            uploadedAt: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000))
          }
        });
        
        console.log(`Added document ${fileName} to policy`);
      }
    } else {
      console.log('Sample documents directory not found. Skipping document creation.');
    }
  }

  console.log('Policy seeding completed.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
