-- CreateTable
CREATE TABLE "AutoLoan" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "employeeId" INTEGER NOT NULL,
    "dniNumber" TEXT NOT NULL,
    "dateOfBirth" DATETIME NOT NULL,
    "incomeSource" TEXT NOT NULL,
    "loanAmount" REAL NOT NULL,
    "totalPrice" REAL NOT NULL,
    "tradeInValue" REAL NOT NULL,
    "downPayment" REAL NOT NULL,
    "remainingPrice" REAL NOT NULL,
    "niv" TEXT NOT NULL,
    "newPreowned" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "make" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "trim" TEXT NOT NULL,
    "applicationDate" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'submitted',
    CONSTRAINT "AutoLoan_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "AutoLoanDocument" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "autoLoanId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "filePath" TEXT NOT NULL,
    "uploadedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "AutoLoanDocument_autoLoanId_fkey" FOREIGN KEY ("autoLoanId") REFERENCES "AutoLoan" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
