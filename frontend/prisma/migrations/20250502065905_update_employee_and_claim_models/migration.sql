/*
  Warnings:

  - You are about to drop the column `illnessDescription` on the `Claim` table. All the data in the column will be lost.
  - You are about to drop the column `illnessType` on the `Claim` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `Employee` table. All the data in the column will be lost.
  - Added the required column `description` to the `Claim` table without a default value. This is not possible if the table is not empty.
  - Added the required column `firstName` to the `Employee` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastName` to the `Employee` table without a default value. This is not possible if the table is not empty.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Claim" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "employeeId" INTEGER NOT NULL,
    "claimType" TEXT NOT NULL DEFAULT 'Critical Illness',
    "description" TEXT NOT NULL,
    "incidentDate" DATETIME,
    "dateFiled" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL,
    CONSTRAINT "Claim_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Claim" ("dateFiled", "employeeId", "id", "status", "description", "claimType")
SELECT "dateFiled", "employeeId", "id", "status", "illnessDescription", "illnessType" FROM "Claim";
DROP TABLE "Claim";
ALTER TABLE "new_Claim" RENAME TO "Claim";
CREATE TABLE "new_Employee" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "address" TEXT,
    "employerName" TEXT,
    "groupId" TEXT,
    "memberId" TEXT
);
INSERT INTO "new_Employee" ("email", "id", "firstName", "lastName")
SELECT "email", "id",
CASE WHEN instr("name", ' ') > 0 THEN substr("name", 1, instr("name", ' ') - 1) ELSE "name" END as "firstName",
CASE WHEN instr("name", ' ') > 0 THEN substr("name", instr("name", ' ') + 1) ELSE '' END as "lastName"
FROM "Employee";
DROP TABLE "Employee";
ALTER TABLE "new_Employee" RENAME TO "Employee";
CREATE UNIQUE INDEX "Employee_email_key" ON "Employee"("email");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
