-- CreateTable
CREATE TABLE "Policy" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "employeeId" INTEGER NOT NULL,
    "policyOwner" TEXT NOT NULL,
    "insured" TEXT NOT NULL,
    "spouse" TEXT,
    "group" TEXT NOT NULL,
    "policyNumber" TEXT NOT NULL,
    "originalEffectiveDate" DATETIME,
    "scheduledEffectiveDate" DATETIME,
    "issuedAge" INTEGER,
    "insuredCoverage" REAL,
    "spouseCoverage" REAL,
    CONSTRAINT "Policy_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "PolicyDocument" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "policyId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "filePath" TEXT NOT NULL,
    "uploadedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "PolicyDocument_policyId_fkey" FOREIGN KEY ("policyId") REFERENCES "Policy" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "Policy_employeeId_key" ON "Policy"("employeeId");
