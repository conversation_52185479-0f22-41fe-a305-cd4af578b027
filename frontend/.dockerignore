# Git
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next
out

# Environment variables
.env
.env.local
.env.*

# Build files
dist
build

# Logs
logs
*.log

# OS specific
.DS_Store
Thumbs.db

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Uploads directory (will be created in the container)
uploads

# Tests
coverage
.nyc_output
