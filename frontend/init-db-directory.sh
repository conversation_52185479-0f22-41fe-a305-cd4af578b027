#!/bin/sh
set -e

# Create the database directory structure
echo "Setting up database directory structure..."
mkdir -p /app/prisma/db

# If dev.db doesn't exist in the mounted volume, create an empty file
if [ ! -f /app/prisma/db/dev.db ]; then
  echo "Creating empty database file..."
  touch /app/prisma/db/dev.db
else
  echo "Existing database file found, preserving data..."
  # Check if the file has content
  if [ -s /app/prisma/db/dev.db ]; then
    echo "Database file contains data."
  else
    echo "WARNING: Database file exists but appears to be empty."
  fi
fi

# Ensure proper permissions
chmod 777 /app/prisma/db
chmod 666 /app/prisma/db/dev.db

echo "Database directory structure setup complete."
