// Test script for claim status API
const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const CLAIM_ID = 9; // Test claim ID

// Test fetching claim status
async function testClaimStatusAPI() {
  console.log('\n--- Testing claim status API ---');
  
  try {
    const response = await fetch(`${BASE_URL}/claim-status/${CLAIM_ID}`);
    
    if (!response.ok) {
      throw new Error(`API returned ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('Claim status data:', JSON.stringify(data, null, 2));
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing claim status API:', error);
  }
}

// Run the test
testClaimStatusAPI();
