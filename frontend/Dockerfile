# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm ci --legacy-peer-deps

# Copy the rest of the application code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the Next.js application
RUN npm run build

# Production stage
FROM node:20-alpine AS runner

WORKDIR /app

# Set environment variables
ENV NODE_ENV=production

# Copy necessary files from the build stage
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/src/generated ./src/generated
# Copy source files needed for OpenAPI documentation
COPY --from=builder /app/src/lib ./src/lib
COPY --from=builder /app/src/app/api ./src/app/api

# Install production dependencies and Prisma for migrations
RUN npm ci --omit=dev --legacy-peer-deps && \
    npm install -g prisma

# Create uploads directory with proper permissions
RUN mkdir -p uploads && chmod 777 uploads

# Copy the scripts
COPY docker-entrypoint.sh init-db-directory.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh /usr/local/bin/init-db-directory.sh

# Expose the port the app will run on
EXPOSE 3000

# Set the entrypoint script
ENTRYPOINT ["docker-entrypoint.sh"]

# Start the application
CMD ["npm", "start"]
