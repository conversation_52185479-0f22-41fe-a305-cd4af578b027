{"extends": "next/core-web-vitals", "ignorePatterns": ["src/generated/**/*", "node_modules/**/*", ".next/**/*", "out/**/*", "public/**/*", "prisma/**/*", "**/*.d.ts", "**/*.config.js", "**/*.config.ts"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-unused-expressions": "warn"}}