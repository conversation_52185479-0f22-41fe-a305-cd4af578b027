// Test script for policy document deletion API
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const POLICY_ID = 1; // Replace with an actual policy ID from your database

// Test deleting a policy document
async function testDeletePolicyDocument() {
  console.log('\n--- Testing policy document deletion ---');
  
  try {
    // Step 1: Get all documents for the policy
    const documentsResponse = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents`);
    const documents = await documentsResponse.json();
    
    if (!documentsResponse.ok) {
      console.log('❌ Failed to fetch policy documents');
      console.log('Error:', documents.error);
      return;
    }
    
    if (documents.length === 0) {
      console.log('⚠️ No documents found for this policy. Please upload a document first.');
      return;
    }
    
    console.log(`Found ${documents.length} documents for policy ${POLICY_ID}`);
    console.log('First document:', documents[0]);
    
    // Step 2: Delete the first document
    const documentToDelete = documents[0];
    const deleteResponse = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents/${documentToDelete.id}`, {
      method: 'DELETE'
    });
    
    const deleteResult = await deleteResponse.json();
    
    if (deleteResponse.ok) {
      console.log('✅ Document deletion successful');
      console.log('Response:', deleteResult);
      
      // Step 3: Verify the document is deleted by trying to fetch it again
      const verifyResponse = await fetch(`${BASE_URL}/policies/${POLICY_ID}/documents/${documentToDelete.id}`);
      
      if (verifyResponse.status === 404) {
        console.log('✅ Verification successful - document no longer exists');
      } else {
        console.log('❌ Verification failed - document still exists');
        const verifyData = await verifyResponse.json();
        console.log('Response:', verifyData);
      }
      
      // Step 4: Verify the file is deleted from the filesystem
      if (fs.existsSync(documentToDelete.filePath)) {
        console.log('❌ File still exists on filesystem:', documentToDelete.filePath);
      } else {
        console.log('✅ File successfully deleted from filesystem');
      }
    } else {
      console.log('❌ Document deletion failed');
      console.log('Error:', deleteResult.error);
    }
  } catch (error) {
    console.log('❌ Test failed with exception');
    console.log('Error:', error.message);
  }
}

// Run the test
async function runTest() {
  console.log('Starting policy document deletion API test...');
  await testDeletePolicyDocument();
  console.log('\nTest completed.');
}

runTest().catch(console.error);
