# CI/CD Pipeline for Claims Customer Application

This document describes the CI/CD pipeline setup for building, testing, and deploying the Claims Customer application to a Kubernetes cluster.

## Overview

The CI/CD pipeline uses GitHub Actions to:
1. Build and test the application
2. Build and push a Docker image to GitHub Container Registry (GHCR)
3. Deploy the application to a Kubernetes cluster

## Prerequisites

- GitHub repository with the Claims Customer application code
- Kubernetes cluster for deployment
- GitHub Secrets configured for Kubernetes access

## GitHub Secrets

The following secrets need to be configured in your GitHub repository:

- `KUBE_CONFIG`: Base64-encoded kubeconfig file for accessing your Kubernetes cluster
- `DOCKER_USERNAME`: Username for the private Docker registry (reg.uniphoredemos.com)
- `DOCKER_PASSWORD`: Password for the private Docker registry

## Pipeline Workflow

### Continuous Integration

On every push to the `main` branch and pull requests:
1. The code is checked out
2. Node.js dependencies are installed
3. Prisma client is generated
4. Tests are run (if available)

### Continuous Deployment

On successful pushes to the `main` branch:
1. A Docker image is built and tagged with the commit SHA
2. The image is pushed to GitHub Container Registry
3. Kubernetes manifests are updated with the new image tag
4. The application is deployed to the Kubernetes cluster

## Kubernetes Resources

The application is deployed with the following Kubernetes resources:

- **ConfigMap**: Contains environment variables for the application
- **PersistentVolumeClaim**:
  - `claims-customer-uploads-pvc`: For storing uploaded files
  - `claims-customer-database-pvc`: For persisting the SQLite database across deployments
- **Deployment**: Runs the application containers
- **Service**: Exposes the application within the cluster
- **Ingress**: Exposes the application to external traffic

## Local Development

For local development and testing, you can use the provided script:

```bash
./scripts/build-and-push.sh
```

This script builds the Docker image and optionally pushes it to the registry.

### Private Registry Configuration

The application uses a private Docker registry at `reg.uniphoredemos.com`. For local development:

1. The credentials are stored in `.env.docker`:
   ```
   DOCKER_REGISTRY=reg.uniphoredemos.com
   DOCKER_USERNAME=solutions
   DOCKER_PASSWORD=Jacada2019!
   IMAGE_NAME=claims-customer
   ```

2. The build scripts automatically load these credentials when pushing images.

> **Note**: For security in production, never commit the actual credentials to the repository. The `.env.docker` file should be added to `.gitignore`.

## Manual Deployment

To manually deploy the application to your Kubernetes cluster:

1. Update the image tag in `k8s/deployment.yaml`
2. Apply the Kubernetes manifests:

```bash
# First create the namespace
kubectl apply -f k8s/namespace.yaml

# Then apply the rest of the resources
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/persistent-volume.yaml
kubectl apply -f k8s/database-pvc.yaml
kubectl apply -f k8s/docker-registry-secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml
```

All resources will be deployed to the `claims-demo` namespace, which is specified in each manifest file.

## Customization

To customize the CI/CD pipeline:

1. Edit `.github/workflows/ci-cd.yaml` to modify the GitHub Actions workflow
2. Edit the Kubernetes manifests in the `k8s/` directory to adjust the deployment configuration
3. Update the `Dockerfile` to modify the container build process

## Known Issues and Solutions

### Database Migrations with Persistent Storage

When making changes to the database schema:

1. **Automatic Migrations**: The application is configured to automatically run `prisma migrate deploy` during container startup, which will apply any pending migrations to the persisted database.

2. **Creating New Migrations**: When you change the Prisma schema, create a new migration locally:
   ```bash
   cd frontend
   npx prisma migrate dev --name your_migration_name
   ```

3. **Testing Migrations**: Always test migrations locally before deploying to production.

4. **Backup Before Migration**: For critical deployments with schema changes, consider backing up the database:
   ```bash
   # Get the pod name
   kubectl get pods -n claims-demo

   # Copy the database file from the pod
   kubectl cp claims-demo/pod-name:/app/prisma/dev.db ./dev.db.backup
   ```

5. **Troubleshooting**: If migrations fail, you can manually apply them by executing commands in the pod:
   ```bash
   kubectl exec -it pod-name -n claims-demo -- npx prisma migrate deploy
   ```

### React Dependency Conflicts

The project uses React 19, but some dependencies (like swagger-ui-react) require React versions less than 19. To handle this:

- The Dockerfile uses `--legacy-peer-deps` flag with npm to bypass peer dependency conflicts
- If you encounter dependency conflicts when building locally, use:
  ```bash
  npm install --legacy-peer-deps
  ```

- This approach allows the application to build successfully, but you should test thoroughly to ensure all components work as expected
