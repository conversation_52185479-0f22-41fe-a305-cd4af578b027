# Required
ANTHROPIC_API_KEY=your-api-key-here  # For most AI ops -- Format: sk-ant-api03-... (Required)
PERPLEXITY_API_KEY=pplx-abcde        # For research -- Format: pplx-abcde (Optional, Highly Recommended)

# X-Stream FAQ API Configuration
XSTREAM_CLIENT_ID=F8CdLuZYwGvYzYFX5d7UxbPAkGDGieS3  # Auth0 client ID for X-Stream API
XSTREAM_CLIENT_SECRET=H0HANQEvrAfNzl1G6D3zLmGf3DFplSCzTXRbhNvFt_3fbmUUEv5AT002NyH0G8t-  # Auth0 client secret for X-Stream API
XSTREAM_FAQ_API_URL=https://api.uniphore.com/faq  # Base URL for X-Stream FAQ API
XSTREAM_LLM_INFERENCE_URL=https://api.us2e2a.cloud.uniphore.com/llm-inference/v1/answer-question-with-rag  # LLM inference API URL
XSTREAM_KB_ID=85420b1b-7d15-4e3f-b737-935762dc5405  # Knowledge base ID for LLM inference API

# Optional - defaults shown
MODEL=claude-3-7-sonnet-20250219  # Recommended models: claude-3-7-sonnet-20250219, claude-3-opus-20240229 (Required)
PERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)
MAX_TOKENS=64000                   # Maximum tokens for model responses (Required)
TEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)
DEBUG=false                       # Enable debug logging (true/false)
LOG_LEVEL=info                    # Log level (debug, info, warn, error)
DEFAULT_SUBTASKS=5                # Default number of subtasks when expanding
DEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)
PROJECT_NAME={{projectName}}      # Project name for tasks.json metadata