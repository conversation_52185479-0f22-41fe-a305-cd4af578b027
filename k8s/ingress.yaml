apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: traefik
    kubernetes.io/tls-acme: "true"
  name: claims-customer-service
  namespace: claims-demo
spec:
  rules:
    - host: claims-portal.uniphoredemos.com
      http:
        paths:
          - backend:
              serviceName: claims-customer
              servicePort: 80
            path: /
  tls:
    - hosts:
        - claims-portal.uniphoredemos.com
status:
  loadBalancer: {}
