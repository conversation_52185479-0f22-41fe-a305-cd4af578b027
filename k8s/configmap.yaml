apiVersion: v1
kind: ConfigMap
metadata:
  name: claims-customer-config
  namespace: claims-demo
data:
  DATABASE_URL: "file:/app/prisma/db/dev.db"
  AIRTABLE_BASE_ID: ${AIRTABLE_BASE_ID}
  AIRTABLE_API_KEY: ${AIRTABLE_API_KEY}
  XSTREAM_LLM_INFERENCE_URL: https://api.us2e2a.cloud.uniphore.com/llm-inference/v1/answer-question-with-rag
  XSTREAM_KB_ID: 85420b1b-7d15-4e3f-b737-935762dc5405
  XSTREAM_CLIENT_ID: ${XSTREAM_CLIENT_ID}
  XSTREAM_CLIENT_SECRET: ${XSTREAM_CLIENT_SECRET}

  # Add other environment variables as needed
