apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: claims-demo
type: kubernetes.io/dockerconfigjson
data:
  # This is a base64 encoded version of:
  # {"auths":{"reg.uniphoredemos.com":{"username":"solutions","password":"Jacada2019!","auth":"c29sdXRpb25zOkphY2FkYTIwMTkh"}}}
  # You can generate this with:
  # echo -n '{"auths":{"reg.uniphoredemos.com":{"username":"solutions","password":"Jacada2019!","auth":"'$(echo -n "solutions:Jacada2019!" | base64)'"}}}' | base64
  .dockerconfigjson: eyJhdXRocyI6eyJyZWcudW5pcGhvcmVkZW1vcy5jb20iOnsidXNlcm5hbWUiOiJzb2x1dGlvbnMiLCJwYXNzd29yZCI6IkphY2FkYTIwMTkhIiwiYXV0aCI6ImMyOXNkWFJwYjI1ek9rcGhZMkZrWVRJd01Ua2gifX19
