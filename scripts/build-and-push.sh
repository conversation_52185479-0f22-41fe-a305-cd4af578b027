#!/bin/bash
set -e

# Load environment variables from .env.docker file if it exists
if [ -f .env.docker ]; then
    echo "Loading Docker registry configuration from .env.docker"
    source .env.docker
fi

# Default values
REGISTRY="${DOCKER_REGISTRY:-reg.uniphoredemos.com}"
IMAGE_NAME="${IMAGE_NAME:-claims-customer}"
TAG=$(git rev-parse --short HEAD)
USERNAME="${DOCKER_USERNAME:-solutions}"
PASSWORD="${DOCKER_PASSWORD:-Jacada2019!}"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
    --registry)
        REGISTRY="$2"
        shift
        shift
        ;;
    --tag)
        TAG="$2"
        shift
        shift
        ;;
    *)
        echo "Unknown option: $1"
        exit 1
        ;;
    esac
done

FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"
LATEST_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:latest"

echo "Building Docker image: ${FULL_IMAGE_NAME}"

# Build the Docker image
podman build --platform linux/amd64 -t "${FULL_IMAGE_NAME}" -t "${LATEST_IMAGE_NAME}" ./frontend

# Ask if the user wants to push the image
read -p "Do you want to push the image to ${REGISTRY}? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Logging in to registry ${REGISTRY}..."
    echo "${PASSWORD}" | podman login ${REGISTRY} -u ${USERNAME} --password-stdin

    echo "Pushing image to registry..."
    podman push "${FULL_IMAGE_NAME}"
    podman push "${LATEST_IMAGE_NAME}"
    echo "Image pushed successfully!"

    # Logout for security
    podman logout ${REGISTRY}
else
    echo "Skipping push to registry."
fi

echo "Build completed successfully!"
