#!/bin/bash
set -e

# Load environment variables from .env.docker file if it exists
if [ -f .env.docker ]; then
    echo "Loading Docker registry configuration from .env.docker"
    source .env.docker
fi

# Default values
NAMESPACE="claims-demo"
IMAGE_TAG=$(git rev-parse --short HEAD)
REGISTRY="${DOCKER_REGISTRY:-reg.uniphoredemos.com}"
IMAGE_NAME="${IMAGE_NAME:-claims-customer}"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
    --namespace)
        NAMESPACE="$2"
        shift
        shift
        ;;
    --tag)
        IMAGE_TAG="$2"
        shift
        shift
        ;;
    --registry)
        REGISTRY="$2"
        shift
        shift
        ;;
    *)
        echo "Unknown option: $1"
        exit 1
        ;;
    esac
done

echo "Deploying to Kubernetes namespace: ${NAMESPACE}"
echo "Using image: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# Apply namespace
kubectl apply -f k8s/namespace.yaml

# Replace placeholders in deployment.yaml
sed -i.bak "s|\${DOCKER_REGISTRY}|${REGISTRY}|g" k8s/deployment.yaml
sed -i.bak "s|\${IMAGE_TAG}|${IMAGE_TAG}|g" k8s/deployment.yaml

# Apply Kubernetes manifests
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/persistent-volume.yaml
kubectl apply -f k8s/database-pvc.yaml
kubectl apply -f k8s/docker-registry-secret.yaml
kubectl delete -f k8s/deployment.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml

# Restore original deployment.yaml
mv k8s/deployment.yaml.bak k8s/deployment.yaml 2>/dev/null || true

# Wait for deployment to complete
kubectl rollout status deployment/claims-customer -n claims-demo --timeout=180s

echo "Deployment completed successfully!"
